# test_anti_detection.py - 反检测系统测试脚本
# 用于验证反检测功能是否正常工作

import time
import random
from anti_detection import anti_detection_manager, HumanBehaviorSimulator, HumanProfile
from anti_detection_monitor import anti_detection_monitor

def test_human_behavior_simulator():
    """测试人类行为模拟器"""
    print("\n=== 测试人类行为模拟器 ===")
    
    # 创建不同技能水平的模拟器
    profiles = [
        HumanProfile(skill_level=0.3, reaction_time_base=0.25),  # 新手
        HumanProfile(skill_level=0.7, reaction_time_base=0.18),  # 中等
        HumanProfile(skill_level=0.9, reaction_time_base=0.12),  # 高手
    ]
    
    for i, profile in enumerate(profiles):
        print(f"\n--- 测试配置 {i+1} (技能水平: {profile.skill_level}) ---")
        simulator = HumanBehaviorSimulator(profile)
        
        # 测试反应时间
        reaction_times = []
        for _ in range(10):
            delay = simulator.get_human_reaction_delay()
            reaction_times.append(delay)
        
        avg_reaction = sum(reaction_times) / len(reaction_times)
        print(f"平均反应时间: {avg_reaction:.3f}s (范围: {min(reaction_times):.3f}-{max(reaction_times):.3f})")
        
        # 测试失误率
        mistakes = 0
        for _ in range(100):
            if simulator.should_make_mistake():
                mistakes += 1
        
        print(f"失误率: {mistakes}%")
        
        # 测试瞄准偏移
        offsets = []
        for _ in range(10):
            offset_x, offset_y = simulator.get_aim_offset(100)  # 100像素距离
            offset_magnitude = (offset_x**2 + offset_y**2)**0.5
            offsets.append(offset_magnitude)
        
        avg_offset = sum(offsets) / len(offsets)
        print(f"平均瞄准偏移: {avg_offset:.2f}像素")

def test_performance_controller():
    """测试性能控制器"""
    print("\n=== 测试性能控制器 ===")
    
    controller = anti_detection_manager.performance_controller
    
    # 模拟射击序列
    print("模拟100次射击...")
    
    for i in range(100):
        should_assist = controller.should_assist_this_shot()
        should_miss = controller.should_force_miss()
        
        # 模拟射击结果
        if should_miss:
            hit = False
            headshot = False
        elif should_assist:
            hit = random.random() < 0.8  # 80%命中率（有辅助）
            headshot = hit and random.random() < 0.3  # 30%爆头率
        else:
            hit = random.random() < 0.2  # 20%命中率（无辅助）
            headshot = hit and random.random() < 0.1  # 10%爆头率
        
        controller.record_shot(hit, headshot)
        
        # 每20发显示一次统计
        if (i + 1) % 20 == 0:
            report = controller.get_performance_report()
            print(f"第{i+1}发: 命中率={report['accuracy']:.2%}, "
                  f"爆头率={report.get('headshot_rate', 0):.2%}, "
                  f"正常范围={report['within_normal_range']}")

def test_adaptive_capture():
    """测试自适应截图"""
    print("\n=== 测试自适应截图 ===")
    
    capture = anti_detection_manager.screen_capture
    
    # 测试不同游戏状态下的截图频率
    states = ["normal", "combat", "menu"]
    
    for state in states:
        print(f"\n--- 测试状态: {state} ---")
        capture_count = 0
        start_time = time.time()
        
        # 模拟1秒内的截图请求
        while time.time() - start_time < 1.0:
            if capture.should_capture_now(state):
                capture_count += 1
            time.sleep(0.001)  # 1ms间隔
        
        print(f"1秒内截图次数: {capture_count}")
        
        # 测试ROI区域
        roi = capture.get_capture_region((1920, 1080))
        print(f"ROI区域: {roi}")

def test_api_mixer():
    """测试API混淆器"""
    print("\n=== 测试API混淆器 ===")
    
    mixer = anti_detection_manager.api_mixer
    
    # 测试鼠标方法随机化
    methods = []
    for _ in range(20):
        method = mixer.get_random_mouse_method()
        methods.append(method)
    
    print("鼠标方法分布:")
    for method in set(methods):
        count = methods.count(method)
        print(f"  {method}: {count}/20 ({count/20*100:.1f}%)")
    
    # 测试截图变化
    print("\n截图变化参数示例:")
    for i in range(5):
        variation = mixer.get_capture_variation()
        print(f"  变化{i+1}: {variation}")

def test_monitoring_system():
    """测试监控系统"""
    print("\n=== 测试监控系统 ===")
    
    # 启动监控
    anti_detection_monitor.start_monitoring()
    
    # 模拟一些活动
    print("模拟5秒活动...")
    for i in range(5):
        # 模拟射击事件
        assisted = random.random() < 0.7
        hit = random.random() < 0.6
        forced_miss = random.random() < 0.1
        
        anti_detection_monitor.record_shot(assisted, hit, forced_miss)
        time.sleep(1)
    
    # 获取摘要
    summary = anti_detection_monitor.get_performance_summary()
    print("\n监控摘要:")
    for key, value in summary.items():
        print(f"  {key}: {value}")
    
    # 获取建议
    recommendations = anti_detection_monitor.get_recommendations()
    print("\n优化建议:")
    for rec in recommendations:
        print(f"  - {rec}")
    
    # 停止监控
    anti_detection_monitor.stop_monitoring()

def run_comprehensive_test():
    """运行综合测试"""
    print("🚀 开始反检测系统综合测试")
    print("=" * 50)
    
    # 初始化反检测系统
    print("初始化反检测系统...")
    anti_detection_manager.initialize()
    
    try:
        # 运行各项测试
        test_human_behavior_simulator()
        test_performance_controller()
        test_adaptive_capture()
        test_api_mixer()
        test_monitoring_system()
        
        print("\n" + "=" * 50)
        print("✅ 所有测试完成")
        
        # 获取系统状态报告
        status = anti_detection_manager.get_status_report()
        print("\n📊 系统状态报告:")
        print(f"反检测系统: {'启用' if status['enabled'] else '禁用'}")
        print(f"进程伪装: {'激活' if status['process_disguised'] else '未激活'}")
        
        human_profile = status['human_profile']
        print(f"人类特征 - 反应时间: {human_profile['reaction_time']:.3f}s, "
              f"技能水平: {human_profile['skill_level']:.2f}")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理
        print("\n清理反检测系统...")
        anti_detection_manager.cleanup()

if __name__ == "__main__":
    run_comprehensive_test()
