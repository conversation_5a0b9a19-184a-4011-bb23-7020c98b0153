# config.py
import ctypes
import json
import os

class Config:
    def __init__(self):
        # 自动获取屏幕分辨率
        user32 = ctypes.windll.user32
        user32.SetProcessDPIAware()
        self.width = user32.GetSystemMetrics(0)
        self.height = user32.GetSystemMetrics(1)

        self.center_x = self.width // 2
        self.center_y = self.height // 2

        # 全屏检测
        self.capture_width = self.width
        self.capture_height = self.height
        self.capture_left = 0
        self.capture_top = 0
        self.crosshairX = self.width // 2
        self.crosshairY = self.height // 2
        self.region = {"top": 0, "left": 0, "width": self.width, "height": self.height}

        # 程序执行状态
        self.Running = True
        self.AimToggle = True

        # ONNX 模型相关设置
        self.model_input_size = 640
        default_model_name = 'Roblox.onnx'
        self.model_path = os.path.join('模型', default_model_name)
        self.current_provider = "CPUExecutionProvider"

        # 瞄准与显示设置
        self.AimKeys = [0x01, 0x06, 0x02]  # 左键 + X2键 + 右键
        self.fov_size = 666
        self.show_confidence = True
        self.min_confidence = 0.66
        self.aim_part = "head"

        # ***** 新增：单目标模式 *****
        self.single_target_mode = True  # 一次最多检测一个离准心最近的敌人

        # ***** 新增：音效提示系统 *****
        self.enable_sound_alert = True  # 启用音效提示
        self.sound_frequency = 1000     # 音效频率 (Hz)
        self.sound_duration = 100       # 音效持续时间 (ms)
        self.sound_interval = 200       # 音效间隔 (ms)

        # 头部和身体区域占比设置
        self.head_width_ratio = 0.38    # 头部宽度占检测框宽度的比例
        self.head_height_ratio = 0.26   # 头部高度占检测框高度的比例
        self.body_width_ratio = 0.87     # 身体宽度占检测框宽度的比例

        # PID 控制器参数 (分离 X 和 Y 轴)
        self.pid_kp_x = 0.26      # 水平 P: 比例 - 主要影响反应速度
        self.pid_ki_x = 0.0       # 水平 I: 积分 - 修正静态误差
        self.pid_kd_x = 0.0       # 水平 D: 微分 - 抑制抖动与过冲
        self.pid_kp_y = 0.26      # 垂直 P: 比例
        self.pid_ki_y = 0.0       # 垂直 I: 积分
        self.pid_kd_y = 0.0       # 垂直 D: 微分

        # 新增：匀速移动模式
        self.constant_speed_mode = False  # 是否使用匀速移动而非PID控制
        self.constant_speed_x = 2.0       # 水平匀速移动速度
        self.constant_speed_y = 2.0       # 垂直匀速移动速度

        # 新增：鼠标移动方式选择
        self.mouse_move_method = "mouse_event"  # 固定使用mouse_event

        # 优化：调整检测间隔为更合理的值，平衡性能和响应速度
        self.detect_interval = 1 / 60  # ***** 修改：将检测间隔设置为每秒 60 次 *****
        self.aim_toggle_key = 45  # Insert 键
        self.auto_fire_key2 = 0x04  # 鼠标中键

        # 自动开枪
        self.auto_fire_key = 0x06   # 鼠标X2键
        self.auto_fire_delay = 0.0  # 无延迟
        self.auto_fire_interval = 0.18 # 射击间隔
        self.auto_fire_target_part = "both" # 可选: "head", "body", "both"

        # 保持检测功能
        self.keep_detecting = True  # 启用保持检测
        # FOV 跟随鼠标
        self.fov_follow_mouse = False



        # 显示开关
        self.show_fov = True
        self.show_boxes = True
        self.show_status_panel = True # ***** 新增此行 *****

        # 优化：性能相关设置
        self.performance_mode = True  # 预设启用性能模式，最大化CPU使用率
        self.max_queue_size = 1  # 减少队列大小，降低延迟

        # 新增：CPU性能优化参数
        self.cpu_optimization = True  # 启用CPU优化
        self.thread_priority = "high"  # 线程优先级：normal, high, realtime
        self.process_priority = "high"  # 进程优先级：normal, high, realtime
        self.cpu_affinity = None  # CPU亲和性设置，None表示使用所有CPU核心

        # 新增：高级/简单模式切换
        self.advanced_mode = True  # True=高级模式，False=简单模式

        # ***** 反检测系统配置 *****
        self.anti_detection_enabled = True  # 启用反检测系统

        # 人类行为模拟配置
        self.human_reaction_time_base = 0.18  # 基础反应时间(秒)
        self.human_skill_level = 0.75  # 技能水平 (0-1)
        self.human_consistency = 0.8  # 操作一致性 (0-1)
        self.human_fatigue_rate = 0.001  # 疲劳累积速率

        # 性能控制配置
        self.target_accuracy = 0.25  # 目标命中率 (25%)
        self.target_headshot_rate = 0.15  # 目标爆头率 (15%)
        self.target_kd_ratio = 1.2  # 目标KD比

        # 自适应截图配置
        self.adaptive_capture_enabled = True  # 启用自适应截图
        self.capture_roi_enabled = True  # 启用ROI截图

        # API混淆配置
        self.api_randomization_enabled = True  # 启用API随机化
        self.mouse_method_randomization = True  # 启用鼠标方法随机化

def save_config(config_instance):
    """将所有可配置的参数保存到 config.json"""
    data = {
        'fov_size': config_instance.fov_size,
        'pid_kp_x': getattr(config_instance, 'pid_kp_x', 0.26),
        'pid_ki_x': getattr(config_instance, 'pid_ki_x', 0.0),
        'pid_kd_x': getattr(config_instance, 'pid_kd_x', 0.0),
        'pid_kp_y': getattr(config_instance, 'pid_kp_y', 0.26),
        'pid_ki_y': getattr(config_instance, 'pid_ki_y', 0.0),
        'pid_kd_y': getattr(config_instance, 'pid_kd_y', 0.0),
        'aim_part': config_instance.aim_part,
        'AimKeys': config_instance.AimKeys,
        'auto_fire_key': getattr(config_instance, 'auto_fire_key', 0x06),
        'auto_fire_delay': getattr(config_instance, 'auto_fire_delay', 0.0),
        'auto_fire_interval': getattr(config_instance, 'auto_fire_interval', 0.18),
        'auto_fire_target_part': getattr(config_instance, 'auto_fire_target_part', "both"),
        'min_confidence': config_instance.min_confidence,
        'show_confidence': config_instance.show_confidence,
        'detect_interval': config_instance.detect_interval,
        'keep_detecting': getattr(config_instance, 'keep_detecting', True),
        'fov_follow_mouse': getattr(config_instance, 'fov_follow_mouse', False),
        'aim_toggle_key': getattr(config_instance, 'aim_toggle_key', 45),


        # ***** 新增的保存项目 *****
        'model_path': getattr(config_instance, 'model_path', os.path.join('模型', 'Roblox.onnx')),
        'auto_fire_key2': getattr(config_instance, 'auto_fire_key2', 0x04),
        'AimToggle': getattr(config_instance, 'AimToggle', True),
        'show_fov': getattr(config_instance, 'show_fov', True),
        'show_boxes': getattr(config_instance, 'show_boxes', True),
        'show_status_panel': getattr(config_instance, 'show_status_panel', True), # ***** 新增此行 *****
        'single_target_mode': getattr(config_instance, 'single_target_mode', True), # ***** 新增：单目标模式 *****

        # 头部和身体区域占比设置
        'head_width_ratio': getattr(config_instance, 'head_width_ratio', 0.38),
        'head_height_ratio': getattr(config_instance, 'head_height_ratio', 0.26),
        'body_width_ratio': getattr(config_instance, 'body_width_ratio', 0.87),

        # 优化：性能相关设置
        'performance_mode': getattr(config_instance, 'performance_mode', True),
        'max_queue_size': getattr(config_instance, 'max_queue_size', 1),

        # 新增：CPU性能优化参数
        'cpu_optimization': getattr(config_instance, 'cpu_optimization', True),
        'thread_priority': getattr(config_instance, 'thread_priority', "high"),
        'process_priority': getattr(config_instance, 'process_priority', "high"),
        'cpu_affinity': getattr(config_instance, 'cpu_affinity', None),

        # ***** 新增：音效提示系统 *****
        'enable_sound_alert': getattr(config_instance, 'enable_sound_alert', True),
        'sound_frequency': getattr(config_instance, 'sound_frequency', 1000),
        'sound_duration': getattr(config_instance, 'sound_duration', 100),
        'sound_interval': getattr(config_instance, 'sound_interval', 200),

        # 新增：高级/简单模式切换
        'advanced_mode': getattr(config_instance, 'advanced_mode', True),

        # 新增：鼠标移动方式
        'mouse_move_method': getattr(config_instance, 'mouse_move_method', 'mouse_event'),

        # ***** 反检测系统配置保存 *****
        'anti_detection_enabled': getattr(config_instance, 'anti_detection_enabled', True),
        'human_reaction_time_base': getattr(config_instance, 'human_reaction_time_base', 0.18),
        'human_skill_level': getattr(config_instance, 'human_skill_level', 0.75),
        'human_consistency': getattr(config_instance, 'human_consistency', 0.8),
        'human_fatigue_rate': getattr(config_instance, 'human_fatigue_rate', 0.001),
        'target_accuracy': getattr(config_instance, 'target_accuracy', 0.25),
        'target_headshot_rate': getattr(config_instance, 'target_headshot_rate', 0.15),
        'target_kd_ratio': getattr(config_instance, 'target_kd_ratio', 1.2),
        'adaptive_capture_enabled': getattr(config_instance, 'adaptive_capture_enabled', True),
        'capture_roi_enabled': getattr(config_instance, 'capture_roi_enabled', True),
        'api_randomization_enabled': getattr(config_instance, 'api_randomization_enabled', True),
        'mouse_method_randomization': getattr(config_instance, 'mouse_method_randomization', True),
    }
    try:
        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print("设置已保存")
    except Exception as e:
        print(f"设置保存失败: {e}")

def load_config(config_instance):
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        for k, v in data.items():
            # 为了兼容旧设置文件，检查新参数是否存在
            if hasattr(config_instance, k):
                setattr(config_instance, k, v)
        print("设置文件已加载")
    except FileNotFoundError:
        print("未找到设置文件，使用默认值")
    except Exception as e:
        print(f"设置加载失败: {e}")