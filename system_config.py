# system_config.py - 系统配置管理
# 提供系统增强参数的配置、保存和加载功能

import json
import os
from dataclasses import dataclass, asdict
from typing import Dict, Any

@dataclass
class SystemConfig:
    """系统增强配置类"""
    
    # 总开关
    enabled: bool = True
    debug_mode: bool = False
    
    # 人类行为模拟配置
    human_reaction_time_base: float = 0.18  # 基础反应时间(秒)
    human_reaction_time_variance: float = 0.05  # 反应时间方差
    human_skill_level: float = 0.75  # 技能水平 (0-1)
    human_consistency: float = 0.8  # 操作一致性 (0-1)
    human_fatigue_rate: float = 0.001  # 疲劳累积速率
    human_stress_sensitivity: float = 0.3  # 压力敏感度
    
    # 性能控制配置
    target_accuracy: float = 0.25  # 目标命中率
    target_headshot_rate: float = 0.15  # 目标爆头率
    target_kd_ratio: float = 1.2  # 目标KD比
    max_accuracy: float = 0.35  # 最大允许命中率
    max_headshot_rate: float = 0.25  # 最大允许爆头率
    
    # 自适应截图配置
    adaptive_capture_enabled: bool = True
    base_capture_fps: int = 60  # 基础截图帧率
    combat_capture_fps: int = 60  # 战斗时截图帧率
    menu_capture_fps: int = 5  # 菜单时截图帧率
    capture_roi_enabled: bool = True  # 启用ROI截图
    roi_max_width: int = 1280  # ROI最大宽度
    roi_max_height: int = 720  # ROI最大高度
    
    # 鼠标移动配置
    mouse_jitter_enabled: bool = True  # 启用鼠标抖动
    mouse_jitter_max: float = 0.5  # 最大抖动像素
    mouse_delay_enabled: bool = True  # 启用移动延迟
    mouse_delay_max: float = 0.002  # 最大延迟(秒)
    movement_smoothing: float = 0.8  # 移动平滑因子
    
    # API混淆配置
    api_randomization_enabled: bool = True
    mouse_method_weights: Dict[str, float] = None  # 鼠标方法权重
    capture_variation_enabled: bool = True
    
    # PID控制器配置
    pid_randomness: float = 0.1  # PID随机性
    pid_smoothing: float = 0.8  # PID平滑因子
    
    # 进程伪装配置
    process_disguise_enabled: bool = True
    disguise_process_name: str = "SystemOptimizer.exe"
    
    def __post_init__(self):
        """初始化后处理"""
        if self.mouse_method_weights is None:
            self.mouse_method_weights = {
                "mouse_event": 0.4,
                "SendInput": 0.3,
                "SetCursorPos": 0.2,
                "direct_input": 0.1
            }

class SystemConfigManager:
    """系统配置管理器"""
    
    def __init__(self, config_file: str = "system_config.json"):
        self.config_file = config_file
        self.config = SystemConfig()
        self.load_config()
    
    def load_config(self) -> bool:
        """从文件加载配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 更新配置对象
                for key, value in data.items():
                    if hasattr(self.config, key):
                        setattr(self.config, key, value)
                
                print(f"[系统配置] ✅ 配置已从 {self.config_file} 加载")
                return True
            else:
                print(f"[系统配置] ⚠️ 配置文件不存在，使用默认配置")
                self.save_config()  # 创建默认配置文件
                return True
                
        except Exception as e:
            print(f"[系统配置] ❌ 加载配置失败: {e}")
            return False
    
    def save_config(self) -> bool:
        """保存配置到文件"""
        try:
            config_dict = asdict(self.config)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=4, ensure_ascii=False)
            
            print(f"[系统配置] ✅ 配置已保存到 {self.config_file}")
            return True
            
        except Exception as e:
            print(f"[系统配置] ❌ 保存配置失败: {e}")
            return False
    
    def update_config(self, **kwargs) -> bool:
        """更新配置参数"""
        try:
            for key, value in kwargs.items():
                if hasattr(self.config, key):
                    setattr(self.config, key, value)
                    print(f"[系统配置] 更新 {key} = {value}")
                else:
                    print(f"[系统配置] ⚠️ 未知配置项: {key}")
            
            return self.save_config()
            
        except Exception as e:
            print(f"[系统配置] ❌ 更新配置失败: {e}")
            return False
    
    def reset_to_defaults(self) -> bool:
        """重置为默认配置"""
        try:
            self.config = SystemConfig()
            return self.save_config()
        except Exception as e:
            print(f"[系统配置] ❌ 重置配置失败: {e}")
            return False
    
    def get_profile_configs(self) -> Dict[str, Dict[str, Any]]:
        """获取预设配置文件"""
        return {
            "conservative": {
                "human_reaction_time_base": 0.22,
                "human_skill_level": 0.6,
                "target_accuracy": 0.2,
                "target_headshot_rate": 0.12,
                "max_accuracy": 0.28,
                "description": "保守模式 - 最大化隐蔽性"
            },
            "balanced": {
                "human_reaction_time_base": 0.18,
                "human_skill_level": 0.75,
                "target_accuracy": 0.25,
                "target_headshot_rate": 0.15,
                "max_accuracy": 0.35,
                "description": "平衡模式 - 性能与安全平衡"
            },
            "aggressive": {
                "human_reaction_time_base": 0.15,
                "human_skill_level": 0.85,
                "target_accuracy": 0.3,
                "target_headshot_rate": 0.2,
                "max_accuracy": 0.4,
                "description": "激进模式 - 更高性能"
            },
            "testing": {
                "human_reaction_time_base": 0.12,
                "human_skill_level": 0.9,
                "target_accuracy": 0.35,
                "target_headshot_rate": 0.25,
                "max_accuracy": 0.45,
                "debug_mode": True,
                "description": "测试模式 - 调试用途"
            }
        }
    
    def apply_profile(self, profile_name: str) -> bool:
        """应用预设配置"""
        try:
            profiles = self.get_profile_configs()
            
            if profile_name not in profiles:
                print(f"[系统配置] ❌ 未知的配置文件: {profile_name}")
                return False
            
            profile_config = profiles[profile_name]
            
            # 应用配置（排除description字段）
            config_updates = {k: v for k, v in profile_config.items() if k != 'description'}
            
            return self.update_config(**config_updates)
            
        except Exception as e:
            print(f"[系统配置] ❌ 应用配置文件失败: {e}")
            return False
    
    def validate_config(self) -> bool:
        """验证配置的有效性"""
        try:
            # 检查数值范围
            if not (0.05 <= self.config.human_reaction_time_base <= 0.5):
                print("[系统配置] ⚠️ 反应时间超出合理范围")
                return False
            
            if not (0.1 <= self.config.human_skill_level <= 1.0):
                print("[系统配置] ⚠️ 技能水平超出合理范围")
                return False
            
            if not (0.1 <= self.config.target_accuracy <= 0.5):
                print("[系统配置] ⚠️ 目标命中率超出合理范围")
                return False
            
            if self.config.target_accuracy >= self.config.max_accuracy:
                print("[系统配置] ⚠️ 目标命中率不应大于等于最大命中率")
                return False
            
            print("[系统配置] ✅ 配置验证通过")
            return True
            
        except Exception as e:
            print(f"[系统配置] ❌ 配置验证失败: {e}")
            return False
    
    def export_config(self, export_file: str) -> bool:
        """导出配置到指定文件"""
        try:
            config_dict = asdict(self.config)
            
            with open(export_file, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=4, ensure_ascii=False)
            
            print(f"[系统配置] ✅ 配置已导出到 {export_file}")
            return True
            
        except Exception as e:
            print(f"[系统配置] ❌ 导出配置失败: {e}")
            return False
    
    def import_config(self, import_file: str) -> bool:
        """从指定文件导入配置"""
        try:
            if not os.path.exists(import_file):
                print(f"[系统配置] ❌ 导入文件不存在: {import_file}")
                return False
            
            with open(import_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 备份当前配置
            backup_config = asdict(self.config)
            
            try:
                # 更新配置
                for key, value in data.items():
                    if hasattr(self.config, key):
                        setattr(self.config, key, value)
                
                # 验证配置
                if self.validate_config():
                    self.save_config()
                    print(f"[系统配置] ✅ 配置已从 {import_file} 导入")
                    return True
                else:
                    # 恢复备份配置
                    for key, value in backup_config.items():
                        setattr(self.config, key, value)
                    print("[系统配置] ❌ 导入的配置无效，已恢复原配置")
                    return False
                    
            except Exception as e:
                # 恢复备份配置
                for key, value in backup_config.items():
                    setattr(self.config, key, value)
                print(f"[系统配置] ❌ 导入配置时出错，已恢复原配置: {e}")
                return False
                
        except Exception as e:
            print(f"[系统配置] ❌ 导入配置失败: {e}")
            return False

# 创建全局配置管理器实例
config_manager = SystemConfigManager()
