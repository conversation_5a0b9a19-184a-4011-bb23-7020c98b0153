# system_monitor.py - 系统状态监控模块
# 实时监控系统增强状态，提供调试和优化信息

import time
import threading
from typing import Dict, List, Optional
from dataclasses import dataclass
from behavior_simulator import behavior_manager

@dataclass
class MonitoringData:
    """监控数据结构"""
    timestamp: float
    human_reaction_time: float
    fatigue_level: float
    stress_level: float
    accuracy: float
    headshot_rate: float
    assist_rate: float
    miss_rate: float

class SystemMonitor:
    """
    系统监控器
    实时监控系统增强的运行状态和效果
    """
    
    def __init__(self):
        self.monitoring_active = False
        self.monitoring_thread = None
        self.data_history: List[MonitoringData] = []
        self.max_history_size = 1000
        
        # 统计数据
        self.session_stats = {
            'total_shots': 0,
            'assisted_shots': 0,
            'forced_misses': 0,
            'natural_misses': 0,
            'session_start': time.time()
        }
        
        # 警告阈值
        self.warning_thresholds = {
            'accuracy_too_high': 0.4,  # 命中率过高警告
            'headshot_rate_too_high': 0.3,  # 爆头率过高警告
            'assist_rate_too_high': 0.9,  # 辅助率过高警告
            'reaction_time_too_fast': 0.1,  # 反应时间过快警告
        }
    
    def start_monitoring(self):
        """启动监控"""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        print("[系统监控] 监控系统已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=1.0)
        print("[系统监控] 监控系统已停止")
    
    def _monitoring_loop(self):
        """监控主循环"""
        while self.monitoring_active:
            try:
                # 收集当前数据
                current_data = self._collect_current_data()
                
                # 添加到历史记录
                self.data_history.append(current_data)
                
                # 保持历史记录大小
                if len(self.data_history) > self.max_history_size:
                    self.data_history.pop(0)
                
                # 检查异常情况
                self._check_warnings(current_data)
                
                # 每5秒监控一次
                time.sleep(5.0)
                
            except Exception as e:
                print(f"[系统监控] 监控循环错误: {e}")
                time.sleep(1.0)
    
    def _collect_current_data(self) -> MonitoringData:
        """收集当前状态数据"""
        try:
            # 获取行为管理器状态
            status = behavior_manager.get_status_report()
            
            # 获取人类行为数据
            human_profile = status.get('human_profile', {})
            performance = status.get('performance', {})
            
            # 计算辅助率和失误率
            assist_rate = self._calculate_assist_rate()
            miss_rate = self._calculate_miss_rate()
            
            return MonitoringData(
                timestamp=time.time(),
                human_reaction_time=human_profile.get('reaction_time', 0.18),
                fatigue_level=human_profile.get('fatigue_level', 0.0),
                stress_level=human_profile.get('stress_level', 0.0),
                accuracy=performance.get('accuracy', 0.0),
                headshot_rate=performance.get('headshot_rate', 0.0),
                assist_rate=assist_rate,
                miss_rate=miss_rate
            )
            
        except Exception as e:
            print(f"[系统监控] 数据收集错误: {e}")
            return MonitoringData(
                timestamp=time.time(),
                human_reaction_time=0.18,
                fatigue_level=0.0,
                stress_level=0.0,
                accuracy=0.0,
                headshot_rate=0.0,
                assist_rate=0.0,
                miss_rate=0.0
            )
    
    def _calculate_assist_rate(self) -> float:
        """计算辅助率"""
        total_shots = self.session_stats['total_shots']
        assisted_shots = self.session_stats['assisted_shots']
        
        if total_shots == 0:
            return 0.0
        
        return assisted_shots / total_shots
    
    def _calculate_miss_rate(self) -> float:
        """计算失误率"""
        total_shots = self.session_stats['total_shots']
        forced_misses = self.session_stats['forced_misses']
        natural_misses = self.session_stats['natural_misses']
        
        if total_shots == 0:
            return 0.0
        
        return (forced_misses + natural_misses) / total_shots
    
    def _check_warnings(self, data: MonitoringData):
        """检查警告条件"""
        warnings = []
        
        # 检查命中率
        if data.accuracy > self.warning_thresholds['accuracy_too_high']:
            warnings.append(f"命中率过高: {data.accuracy:.1%}")
        
        # 检查爆头率
        if data.headshot_rate > self.warning_thresholds['headshot_rate_too_high']:
            warnings.append(f"爆头率过高: {data.headshot_rate:.1%}")
        
        # 检查辅助率
        if data.assist_rate > self.warning_thresholds['assist_rate_too_high']:
            warnings.append(f"辅助率过高: {data.assist_rate:.1%}")
        
        # 检查反应时间
        if data.human_reaction_time < self.warning_thresholds['reaction_time_too_fast']:
            warnings.append(f"反应时间过快: {data.human_reaction_time:.3f}s")
        
        # 输出警告
        if warnings:
            print(f"[系统监控] ⚠️ 检测到异常: {'; '.join(warnings)}")
    
    def record_shot(self, assisted: bool, hit: bool, forced_miss: bool = False):
        """记录射击事件"""
        self.session_stats['total_shots'] += 1
        
        if assisted:
            self.session_stats['assisted_shots'] += 1
        
        if not hit:
            if forced_miss:
                self.session_stats['forced_misses'] += 1
            else:
                self.session_stats['natural_misses'] += 1
    
    def get_performance_summary(self) -> Dict:
        """获取性能摘要"""
        if not self.data_history:
            return {}
        
        # 计算平均值
        total_data_points = len(self.data_history)
        
        avg_reaction_time = sum(d.human_reaction_time for d in self.data_history) / total_data_points
        avg_fatigue_level = sum(d.fatigue_level for d in self.data_history) / total_data_points
        avg_stress_level = sum(d.stress_level for d in self.data_history) / total_data_points
        avg_accuracy = sum(d.accuracy for d in self.data_history) / total_data_points
        avg_headshot_rate = sum(d.headshot_rate for d in self.data_history) / total_data_points
        avg_assist_rate = sum(d.assist_rate for d in self.data_history) / total_data_points
        avg_miss_rate = sum(d.miss_rate for d in self.data_history) / total_data_points
        
        # 会话时长
        session_duration = time.time() - self.session_stats['session_start']
        
        return {
            'session_duration_minutes': session_duration / 60,
            'total_shots': self.session_stats['total_shots'],
            'assist_rate': avg_assist_rate,
            'miss_rate': avg_miss_rate,
            'avg_accuracy': avg_accuracy,
            'avg_headshot_rate': avg_headshot_rate,
            'avg_reaction_time': avg_reaction_time,
            'avg_fatigue_level': avg_fatigue_level,
            'avg_stress_level': avg_stress_level,
            'data_points_collected': total_data_points,
            'monitoring_active': self.monitoring_active
        }
    
    def get_recommendations(self) -> List[str]:
        """获取优化建议"""
        recommendations = []
        
        if not self.data_history:
            return ["暂无足够数据提供建议"]
        
        summary = self.get_performance_summary()
        
        # 基于数据提供建议
        if summary['avg_accuracy'] > 0.35:
            recommendations.append("建议降低目标命中率或增加失误率")
        
        if summary['avg_headshot_rate'] > 0.25:
            recommendations.append("建议降低爆头率设置")
        
        if summary['avg_reaction_time'] < 0.12:
            recommendations.append("建议增加反应时间以提高真实性")
        
        if summary['assist_rate'] > 0.85:
            recommendations.append("建议降低辅助频率")
        
        if summary['avg_fatigue_level'] < 0.1 and summary['session_duration_minutes'] > 30:
            recommendations.append("建议增加疲劳累积速率")
        
        if not recommendations:
            recommendations.append("当前配置表现良好，无需调整")
        
        return recommendations
    
    def export_data(self, filename: Optional[str] = None) -> str:
        """导出监控数据到文件"""
        if filename is None:
            filename = f"system_log_{int(time.time())}.txt"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("# 系统监控数据导出\n")
                f.write(f"# 导出时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"# 数据点数量: {len(self.data_history)}\n\n")
                
                # 写入摘要
                summary = self.get_performance_summary()
                f.write("## 性能摘要\n")
                for key, value in summary.items():
                    f.write(f"{key}: {value}\n")
                
                f.write("\n## 优化建议\n")
                recommendations = self.get_recommendations()
                for i, rec in enumerate(recommendations, 1):
                    f.write(f"{i}. {rec}\n")
                
                # 写入详细数据
                f.write("\n## 详细数据\n")
                f.write("timestamp,reaction_time,fatigue_level,stress_level,accuracy,headshot_rate,assist_rate,miss_rate\n")
                
                for data in self.data_history:
                    f.write(f"{data.timestamp},{data.human_reaction_time:.3f},"
                           f"{data.fatigue_level:.3f},{data.stress_level:.3f},"
                           f"{data.accuracy:.3f},{data.headshot_rate:.3f},"
                           f"{data.assist_rate:.3f},{data.miss_rate:.3f}\n")
            
            print(f"[系统监控] 数据已导出到: {filename}")
            return filename
            
        except Exception as e:
            print(f"[系统监控] 数据导出失败: {e}")
            return ""

# 创建全局监控实例
system_monitor = SystemMonitor()
