# status_indicator.py - 系统状态指示器
# 在主界面显示简洁的系统增强状态信息

import tkinter as tk
from tkinter import ttk
import threading
import time
from typing import Optional

class SystemStatusIndicator:
    """
    系统状态指示器
    在主界面显示简洁的系统增强状态
    """
    
    def __init__(self, parent_frame):
        self.parent_frame = parent_frame
        self.indicator_frame = None
        self.status_vars = {}
        self.update_thread = None
        self.update_running = False
        
        self.create_indicator()
        
    def create_indicator(self):
        """创建状态指示器"""
        # 创建指示器框架
        self.indicator_frame = tk.LabelFrame(
            self.parent_frame, 
            text="🛡️ 系统增强状态", 
            font=("Arial", 10, "bold"),
            bg="#2b2b2b",
            fg="#ffffff",
            bd=2,
            relief="groove"
        )
        
        # 状态变量
        self.status_vars = {
            'system_status': tk.StringVar(value="检查中..."),
            'reaction_time': tk.StringVar(value="--"),
            'accuracy': tk.StringVar(value="--"),
            'risk_level': tk.StringVar(value="--")
        }
        
        # 创建状态显示
        self.create_status_display()
        
        # 启动状态更新
        self.start_status_update()
        
    def create_status_display(self):
        """创建状态显示"""
        # 系统状态
        status_frame = tk.Frame(self.indicator_frame, bg="#2b2b2b")
        status_frame.pack(fill="x", padx=5, pady=2)
        
        tk.Label(status_frame, text="系统:", bg="#2b2b2b", fg="#ffffff", font=("Arial", 9)).pack(side="left")
        tk.Label(status_frame, textvariable=self.status_vars['system_status'], 
                bg="#2b2b2b", fg="#00ff00", font=("Arial", 9, "bold")).pack(side="left", padx=5)
        
        # 反应时间
        reaction_frame = tk.Frame(self.indicator_frame, bg="#2b2b2b")
        reaction_frame.pack(fill="x", padx=5, pady=2)
        
        tk.Label(reaction_frame, text="反应:", bg="#2b2b2b", fg="#ffffff", font=("Arial", 9)).pack(side="left")
        tk.Label(reaction_frame, textvariable=self.status_vars['reaction_time'], 
                bg="#2b2b2b", fg="#ffff00", font=("Arial", 9)).pack(side="left", padx=5)
        
        # 命中率
        accuracy_frame = tk.Frame(self.indicator_frame, bg="#2b2b2b")
        accuracy_frame.pack(fill="x", padx=5, pady=2)
        
        tk.Label(accuracy_frame, text="命中:", bg="#2b2b2b", fg="#ffffff", font=("Arial", 9)).pack(side="left")
        tk.Label(accuracy_frame, textvariable=self.status_vars['accuracy'], 
                bg="#2b2b2b", fg="#00ffff", font=("Arial", 9)).pack(side="left", padx=5)
        
        # 风险等级
        risk_frame = tk.Frame(self.indicator_frame, bg="#2b2b2b")
        risk_frame.pack(fill="x", padx=5, pady=2)
        
        tk.Label(risk_frame, text="风险:", bg="#2b2b2b", fg="#ffffff", font=("Arial", 9)).pack(side="left")
        self.risk_label = tk.Label(risk_frame, textvariable=self.status_vars['risk_level'], 
                                  bg="#2b2b2b", fg="#ff0000", font=("Arial", 9, "bold"))
        self.risk_label.pack(side="left", padx=5)
        
    def pack(self, **kwargs):
        """打包指示器"""
        if self.indicator_frame:
            self.indicator_frame.pack(**kwargs)
    
    def start_status_update(self):
        """启动状态更新"""
        if not self.update_running:
            self.update_running = True
            self.update_thread = threading.Thread(target=self._update_loop, daemon=True)
            self.update_thread.start()
    
    def stop_status_update(self):
        """停止状态更新"""
        self.update_running = False
    
    def _update_loop(self):
        """状态更新循环"""
        while self.update_running:
            try:
                self.update_status()
                time.sleep(3.0)  # 每3秒更新一次
            except Exception as e:
                print(f"[状态指示器] 更新错误: {e}")
                time.sleep(5.0)
    
    def update_status(self):
        """更新状态显示"""
        try:
            # 尝试导入系统增强模块
            from behavior_simulator import behavior_manager
            from system_monitor import system_monitor
            
            # 获取状态报告
            status = behavior_manager.get_status_report()
            
            # 更新系统状态
            if status.get('enabled', False):
                self.status_vars['system_status'].set("✅ 运行")
                system_color = "#00ff00"
            else:
                self.status_vars['system_status'].set("❌ 禁用")
                system_color = "#ff0000"
            
            # 更新反应时间
            human_profile = status.get('human_profile', {})
            reaction_time = human_profile.get('reaction_time', 0)
            self.status_vars['reaction_time'].set(f"{reaction_time:.3f}s")
            
            # 更新命中率
            performance = status.get('performance', {})
            accuracy = performance.get('accuracy', 0)
            self.status_vars['accuracy'].set(f"{accuracy:.1%}")
            
            # 计算风险等级
            risk_level, risk_color = self._calculate_risk_level(status)
            self.status_vars['risk_level'].set(risk_level)
            
            # 更新风险等级颜色
            if hasattr(self, 'risk_label'):
                self.risk_label.configure(fg=risk_color)
                
        except ImportError:
            # 系统增强模块未安装
            self.status_vars['system_status'].set("未安装")
            self.status_vars['reaction_time'].set("--")
            self.status_vars['accuracy'].set("--")
            self.status_vars['risk_level'].set("未知")
            
        except Exception as e:
            # 其他错误
            self.status_vars['system_status'].set("错误")
            self.status_vars['reaction_time'].set("--")
            self.status_vars['accuracy'].set("--")
            self.status_vars['risk_level'].set("错误")
    
    def _calculate_risk_level(self, status):
        """计算风险等级"""
        try:
            human_profile = status.get('human_profile', {})
            performance = status.get('performance', {})
            
            reaction_time = human_profile.get('reaction_time', 0.18)
            accuracy = performance.get('accuracy', 0)
            headshot_rate = performance.get('headshot_rate', 0)
            
            risk_score = 0
            
            # 反应时间风险评估
            if reaction_time < 0.1:
                risk_score += 3  # 高风险
            elif reaction_time < 0.15:
                risk_score += 2  # 中风险
            elif reaction_time < 0.2:
                risk_score += 1  # 低风险
            
            # 命中率风险评估
            if accuracy > 0.4:
                risk_score += 3  # 高风险
            elif accuracy > 0.3:
                risk_score += 2  # 中风险
            elif accuracy > 0.25:
                risk_score += 1  # 低风险
            
            # 爆头率风险评估
            if headshot_rate > 0.3:
                risk_score += 2  # 高风险
            elif headshot_rate > 0.2:
                risk_score += 1  # 中风险
            
            # 确定风险等级和颜色
            if risk_score >= 6:
                return "🔴 极高", "#ff0000"
            elif risk_score >= 4:
                return "🟠 高", "#ff8800"
            elif risk_score >= 2:
                return "🟡 中", "#ffff00"
            else:
                return "🟢 低", "#00ff00"
                
        except Exception:
            return "❓ 未知", "#888888"
    
    def destroy(self):
        """销毁指示器"""
        self.stop_status_update()
        if self.indicator_frame:
            self.indicator_frame.destroy()

class SystemMiniPanel:
    """
    系统迷你面板
    提供快速访问常用功能
    """
    
    def __init__(self, parent_window):
        self.parent = parent_window
        self.window = None
        
    def show(self):
        """显示迷你面板"""
        if self.window and self.window.winfo_exists():
            self.window.lift()
            return
            
        self.create_window()
    
    def create_window(self):
        """创建迷你面板窗口"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("🛡️ 系统增强快速控制")
        self.window.geometry("300x200")
        self.window.resizable(False, False)
        
        # 设置窗口样式
        self.window.configure(bg="#2b2b2b")
        
        # 创建控件
        self.create_controls()
        
    def create_controls(self):
        """创建控制控件"""
        # 标题
        title_label = tk.Label(self.window, text="🛡️ 系统增强快速控制", 
                              font=("Arial", 14, "bold"), 
                              bg="#2b2b2b", fg="#ffffff")
        title_label.pack(pady=10)
        
        # 预设配置按钮
        preset_frame = tk.Frame(self.window, bg="#2b2b2b")
        preset_frame.pack(pady=10)
        
        tk.Button(preset_frame, text="🛡️ 保守模式", 
                 command=lambda: self.apply_preset('conservative'),
                 bg="#4CAF50", fg="white", font=("Arial", 10), width=12).pack(pady=2)
        
        tk.Button(preset_frame, text="⚖️ 平衡模式", 
                 command=lambda: self.apply_preset('balanced'),
                 bg="#2196F3", fg="white", font=("Arial", 10), width=12).pack(pady=2)
        
        tk.Button(preset_frame, text="⚡ 激进模式", 
                 command=lambda: self.apply_preset('aggressive'),
                 bg="#FF9800", fg="white", font=("Arial", 10), width=12).pack(pady=2)
        
        # 控制按钮
        control_frame = tk.Frame(self.window, bg="#2b2b2b")
        control_frame.pack(pady=10)
        
        tk.Button(control_frame, text="📊 详细面板", 
                 command=self.show_full_panel,
                 bg="#9C27B0", fg="white", font=("Arial", 10), width=12).pack(side="left", padx=5)
        
        tk.Button(control_frame, text="❌ 关闭", 
                 command=self.window.destroy,
                 bg="#F44336", fg="white", font=("Arial", 10), width=12).pack(side="left", padx=5)
    
    def apply_preset(self, preset_name):
        """应用预设配置"""
        try:
            from system_config import config_manager
            if config_manager.apply_profile(preset_name):
                tk.messagebox.showinfo("成功", f"已应用 {preset_name} 配置")
            else:
                tk.messagebox.showerror("错误", f"应用 {preset_name} 配置失败")
        except ImportError:
            tk.messagebox.showerror("错误", "系统增强模块未安装")
        except Exception as e:
            tk.messagebox.showerror("错误", f"应用配置时出错: {e}")
    
    def show_full_panel(self):
        """显示完整的高级设置面板"""
        try:
            from advanced_settings import show_advanced_settings_panel
            show_advanced_settings_panel(self.parent)
            self.window.destroy()  # 关闭迷你面板
        except ImportError:
            tk.messagebox.showerror("错误", "高级设置GUI模块未安装")
        except Exception as e:
            tk.messagebox.showerror("错误", f"打开详细面板失败: {e}")

# 便捷函数
def create_system_status_indicator(parent_frame):
    """创建系统状态指示器"""
    return SystemStatusIndicator(parent_frame)

def show_system_mini_panel(parent_window):
    """显示系统迷你面板"""
    mini_panel = SystemMiniPanel(parent_window)
    mini_panel.show()
    return mini_panel
