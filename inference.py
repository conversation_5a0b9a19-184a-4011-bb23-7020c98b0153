# inference.py
import cv2
import numpy as np

class PIDController:
    """
    PID 控制器 - 已集成系统增强功能
    添加了人性化响应和随机化特性
    """
    def __init__(self, Kp, Ki, Kd):
        self.Kp = Kp  # 比例 Proportional
        self.Ki = Ki  # 积分 Integral
        self.Kd = Kd  # 微分 Derivative
        self.reset()

        # ***** 系统增强：人性化参数 *****
        self.response_randomness = 0.1  # 响应随机性
        self.smoothing_factor = 0.8  # 平滑因子
        self.last_output = 0.0  # 上次输出

        import random
        self.random = random

    def reset(self):
        """重置控制器状态"""
        self.integral = 0.0
        self.previous_error = 0.0
        self.last_output = 0.0

    def update(self, error):
        """
        根据当前误差计算控制输出 - 反检测版本
        :param error: 当前误差 (例如, target_x - current_x)
        :return: 控制量 (例如, 鼠标应移动的量)
        """
        # 积分项
        self.integral += error

        # 微分项
        derivative = error - self.previous_error

        # 调整P参数的响应曲线
        # 50%以下保持原始比例，50%以上逐渐放大到200%
        adjusted_kp = self.calculate_adjusted_kp(self.Kp)

        # ***** 系统增强：添加人性化随机性 *****
        # 性能优化：减少随机数生成次数
        random_factor = self.random.uniform(-self.response_randomness, self.response_randomness)
        kp_variation = 1.0 + random_factor
        ki_variation = 1.0 + random_factor * 0.8  # 稍微不同的变化
        kd_variation = 1.0 + random_factor * 1.2

        # 计算输出
        output = (adjusted_kp * kp_variation * error) + \
                (self.Ki * ki_variation * self.integral) + \
                (self.Kd * kd_variation * derivative)

        # ***** 系统增强：输出平滑化，避免机械式响应 *****
        # 性能优化：预计算平滑因子
        inv_smoothing = 1 - self.smoothing_factor
        smoothed_output = self.smoothing_factor * self.last_output + inv_smoothing * output

        # 更新状态
        self.previous_error = error
        self.last_output = smoothed_output

        return smoothed_output

    def calculate_adjusted_kp(self, kp):
        """
        计算调整后的P参数
        50%以下保持原始比例，50%以上逐渐放大到200%
        """
        if kp <= 0.5:
            # 50%以下保持不变
            return kp
        else:
            # 50%以上按比例放大
            # 当kp=0.5时，输出=0.5
            # 当kp=1.0时，输出=2.0
            # 使用线性插值：y = 0.5 + (kp - 0.5) * 3
            # 这样kp从0.5到1.0，输出从0.5到2.0
            return 0.5 + (kp - 0.5) * 3.0

def preprocess_image(image, model_input_size):
    """预处理图像以适配ONNX模型"""
    resized = cv2.resize(image, (model_input_size, model_input_size))
    rgb_image = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)
    normalized = rgb_image.astype(np.float32) / 255.0
    input_tensor = np.transpose(normalized, (2, 0, 1))
    input_tensor = np.expand_dims(input_tensor, axis=0)
    return input_tensor

def postprocess_outputs(outputs, original_width, original_height, model_input_size, min_confidence):
    """后处理ONNX模型输出"""
    predictions = outputs[0][0].T
    boxes, confidences = [], []
    scale_x = original_width / model_input_size
    scale_y = original_height / model_input_size

    for detection in predictions:
        confidence = detection[4]
        if confidence >= min_confidence:
            cx, cy, w, h = detection[:4]
            x1 = (cx - w / 2) * scale_x
            y1 = (cy - h / 2) * scale_y
            x2 = (cx + w / 2) * scale_x
            y2 = (cy + h / 2) * scale_y
            boxes.append([x1, y1, x2, y2])
            confidences.append(confidence)

    return boxes, confidences

def non_max_suppression(boxes, confidences, iou_threshold=0.4):
    """非极大值抑制"""
    if len(boxes) == 0:
        return [], []

    boxes = np.array(boxes)
    confidences = np.array(confidences)
    areas = (boxes[:, 2] - boxes[:, 0]) * (boxes[:, 3] - boxes[:, 1])
    order = confidences.argsort()[::-1]

    keep = []
    while len(order) > 0:
        i = order[0]
        keep.append(i)
        if len(order) == 1:
            break

        xx1 = np.maximum(boxes[i, 0], boxes[order[1:], 0])
        yy1 = np.maximum(boxes[i, 1], boxes[order[1:], 1])
        xx2 = np.minimum(boxes[i, 2], boxes[order[1:], 2])
        yy2 = np.minimum(boxes[i, 3], boxes[order[1:], 3])

        w = np.maximum(0, xx2 - xx1)
        h = np.maximum(0, yy2 - yy1)
        intersection = w * h
        union = areas[i] + areas[order[1:]] - intersection
        iou = intersection / union

        order = order[1:][iou <= iou_threshold]

    return boxes[keep].tolist(), confidences[keep].tolist()