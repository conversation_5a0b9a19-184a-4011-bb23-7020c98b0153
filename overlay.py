# overlay.py
import sys
from PyQt6.QtWidgets import QApplication, QWidget
from PyQt6.QtGui import QPainter, QColor, QPen, QFont
from PyQt6.QtCore import Qt, QTimer
import ctypes
from ctypes import wintypes

class PyQtOverlay(QWidget):
    def __init__(self, boxes_queue, confidences_queue, config):
        super().__init__()
        self.boxes_queue = boxes_queue
        self.confidences_queue = confidences_queue
        self.config = config

        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint |
            Qt.WindowType.WindowStaysOnTopHint |
            Qt.WindowType.Tool
        )
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents)

        self.setGeometry(0, 0, config.width, config.height)
        self.boxes = []
        self.confidences = []

        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_overlay)

        # 优化：使用检测间隔来统一更新频率，转换为毫秒
        if getattr(config, 'performance_mode', False):
            # 性能模式下使用更短的更新间隔
            update_interval_ms = max(int(config.detect_interval * 1000), 1)  # 最小1ms，极高频率
        else:
            update_interval_ms = max(int(config.detect_interval * 1000), 8)  # 最小8ms，避免过高频率
        self.timer.start(update_interval_ms)
        print(f"覆盖层更新间隔设置为: {update_interval_ms}ms (与检测间隔 {config.detect_interval}s 同步)")

        self.show()
        self.set_click_through()

    def set_click_through(self):
        try:
            hwnd = self.winId().__int__()
            GWL_EXSTYLE = -20
            WS_EX_LAYERED = 0x80000
            WS_EX_TRANSPARENT = 0x20
            style = ctypes.windll.user32.GetWindowLongW(hwnd, GWL_EXSTYLE)
            style |= WS_EX_LAYERED | WS_EX_TRANSPARENT
            ctypes.windll.user32.SetWindowLongW(hwnd, GWL_EXSTYLE, style)
        except Exception as e:
            print(f"鼠标穿透设置失败: {e}")

    def update_overlay(self):
        # 优化：批量获取队列数据，减少锁定时间
        new_boxes = None
        new_confidences = None

        try:
            new_boxes = self.boxes_queue.get_nowait()
        except:
            pass

        try:
            new_confidences = self.confidences_queue.get_nowait()
        except:
            pass

        # 只有获取到新数据时才更新
        if new_boxes is not None:
            self.boxes = new_boxes
        if new_confidences is not None:
            self.confidences = new_confidences

        # 优化：只有在启用时才触发重绘
        if self.config.AimToggle:
            self.update()

    def draw_corner_box(self, painter, x1, y1, x2, y2, corner_size=15):
        """绘制四个角点"""
        # 设置点的大小
        point_size = 3

        # 左上角点
        painter.drawEllipse(x1 - point_size//2, y1 - point_size//2, point_size, point_size)

        # 右上角点
        painter.drawEllipse(x2 - point_size//2, y1 - point_size//2, point_size, point_size)

        # 左下角点
        painter.drawEllipse(x1 - point_size//2, y2 - point_size//2, point_size, point_size)

        # 右下角点
        painter.drawEllipse(x2 - point_size//2, y2 - point_size//2, point_size, point_size)

    def paintEvent(self, event):
        if not self.config.AimToggle: # 如果关闭了功能，就不绘制
            return

        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # 性能优化：缓存显示标记，减少属性访问
        if not hasattr(self, '_cached_show_fov'):
            self._cached_show_fov = getattr(self.config, 'show_fov', True)
            self._cached_show_boxes = getattr(self.config, 'show_boxes', True)
            self._cached_show_confidence = self.config.show_confidence
            self._cache_update_counter = 0

        # 每100帧更新一次缓存
        self._cache_update_counter += 1
        if self._cache_update_counter >= 100:
            self._cached_show_fov = getattr(self.config, 'show_fov', True)
            self._cached_show_boxes = getattr(self.config, 'show_boxes', True)
            self._cached_show_confidence = self.config.show_confidence
            self._cache_update_counter = 0

        show_fov = self._cached_show_fov
        show_boxes = self._cached_show_boxes

        # 根据开关决定是否画FOV
        if show_fov:
            fov = self.config.fov_size
            cx, cy = self.config.crosshairX, self.config.crosshairY
            pen = QPen(QColor(255, 0, 0, 180), 1)
            painter.setPen(pen)
            painter.drawRect(cx - fov // 2, cy - fov // 2, fov, fov)

        # 根据开关决定是否画人物框与概率
        if show_boxes and self.boxes:
            pen_box = QPen(QColor(0, 255, 0, 200), 2)  # 增加线条粗细
            painter.setPen(pen_box)

            # 优化：使用缓存的信心度显示设置
            show_confidence = self._cached_show_confidence
            if show_confidence:
                pen_text = QPen(QColor(255, 255, 0, 220), 1)
                font = QFont('Arial', 9, QFont.Weight.Bold)  # 缩小字体
                painter.setFont(font)

            for i, box in enumerate(self.boxes):
                x1, y1, x2, y2 = map(int, box)

                # 使用新的角框绘制方法
                self.draw_corner_box(painter, x1, y1, x2, y2)

                if show_confidence and i < len(self.confidences):
                    confidence = self.confidences[i]
                    text = f"{confidence:.0%}"
                    painter.setPen(pen_text)
                    # 将文字移到左上角外侧，并增加距离
                    painter.drawText(x1 - 20, y1 - 15, text)
                    painter.setPen(pen_box)

def start_pyqt_overlay(boxes_queue, confidences_queue, config):
    print("警告: start_pyqt_overlay 函数已被弃用，UI 启动逻辑已移至 main.py。")
    pass