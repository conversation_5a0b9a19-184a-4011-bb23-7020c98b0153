# system_setup.py - 系统增强快速设置脚本
# 提供简单的命令行界面来配置和启动系统增强功能

import os
import sys
import time
from typing import Optional

def print_banner():
    """打印启动横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    🛡️ Axiom 系统增强工具                      ║
║                     快速启动配置工具                          ║
╠══════════════════════════════════════════════════════════════╣
║  版本: 1.0.0                                                ║
║  状态: 实验性功能                                            ║
║  用途: 技术研究和学习                                        ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(banner)

def check_dependencies():
    """检查依赖项"""
    print("🔍 检查系统依赖...")
    
    required_modules = [
        'behavior_simulator',
        'system_config', 
        'system_monitor',
        'advanced_settings'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"  ✅ {module}")
        except ImportError:
            print(f"  ❌ {module} - 未找到")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n⚠️ 缺少必要模块: {', '.join(missing_modules)}")
        print("请确保所有系统增强模块文件都在当前目录中")
        return False
    
    print("✅ 所有依赖项检查通过\n")
    return True

def show_system_status():
    """显示系统状态"""
    try:
        from behavior_simulator import behavior_manager
        from system_config import config_manager
        
        print("📊 当前系统状态:")
        print("-" * 40)
        
        # 获取状态报告
        status = behavior_manager.get_status_report()
        
        print(f"系统增强: {'✅ 启用' if status['enabled'] else '❌ 禁用'}")
        print(f"进程伪装: {'✅ 激活' if status['process_disguised'] else '❌ 未激活'}")
        
        # 人类行为配置
        human_profile = status.get('human_profile', {})
        print(f"反应时间: {human_profile.get('reaction_time', 0):.3f}s")
        print(f"技能水平: {human_profile.get('skill_level', 0):.2f}")
        print(f"疲劳度: {human_profile.get('fatigue_level', 0):.2f}")
        print(f"压力度: {human_profile.get('stress_level', 0):.2f}")
        
        # 性能统计
        performance = status.get('performance', {})
        print(f"当前命中率: {performance.get('accuracy', 0):.1%}")
        print(f"爆头率: {performance.get('headshot_rate', 0):.1%}")
        
        # 配置信息
        config = config_manager.config
        print(f"\n当前配置模式: {get_current_profile_name(config)}")
        print(f"目标命中率: {config.target_accuracy:.1%}")
        print(f"目标爆头率: {config.target_headshot_rate:.1%}")
        
        print("-" * 40)
        
    except Exception as e:
        print(f"❌ 获取系统状态失败: {e}")

def get_current_profile_name(config):
    """获取当前配置文件名称"""
    from system_config import config_manager
    
    profiles = config_manager.get_profile_configs()
    
    # 简单匹配逻辑
    if abs(config.target_accuracy - 0.2) < 0.05:
        return "保守模式"
    elif abs(config.target_accuracy - 0.25) < 0.05:
        return "平衡模式"
    elif abs(config.target_accuracy - 0.3) < 0.05:
        return "激进模式"
    else:
        return "自定义配置"

def show_menu():
    """显示主菜单"""
    menu = """
🎛️ 系统增强控制菜单:

1. 📊 查看系统状态
2. ⚙️ 配置管理
3. 🎮 预设模式
4. 📈 性能监控
5. 🧪 运行测试
6. 🚀 启动主程序
7. ❌ 退出

请选择操作 (1-7): """
    
    return input(menu).strip()

def config_menu():
    """配置管理菜单"""
    while True:
        config_options = """
⚙️ 配置管理:

1. 📋 显示当前配置
2. ✏️ 修改反应时间
3. 🎯 修改技能水平
4. 🎲 修改目标命中率
5. 💾 保存配置
6. 🔄 重置为默认
7. 🔙 返回主菜单

请选择操作 (1-7): """
        
        choice = input(config_options).strip()
        
        if choice == '1':
            show_current_config()
        elif choice == '2':
            modify_reaction_time()
        elif choice == '3':
            modify_skill_level()
        elif choice == '4':
            modify_target_accuracy()
        elif choice == '5':
            save_config()
        elif choice == '6':
            reset_config()
        elif choice == '7':
            break
        else:
            print("❌ 无效选择，请重试")

def show_current_config():
    """显示当前配置"""
    try:
        from system_config import config_manager
        
        config = config_manager.config
        print("\n📋 当前配置:")
        print("-" * 30)
        print(f"反应时间: {config.human_reaction_time_base:.3f}s")
        print(f"技能水平: {config.human_skill_level:.2f}")
        print(f"操作一致性: {config.human_consistency:.2f}")
        print(f"目标命中率: {config.target_accuracy:.1%}")
        print(f"目标爆头率: {config.target_headshot_rate:.1%}")
        print(f"鼠标抖动: {config.mouse_jitter_max:.1f}px")
        print(f"PID随机性: {config.pid_randomness:.2f}")
        print("-" * 30)
        
    except Exception as e:
        print(f"❌ 显示配置失败: {e}")

def modify_reaction_time():
    """修改反应时间"""
    try:
        from system_config import config_manager
        
        current = config_manager.config.human_reaction_time_base
        print(f"\n当前反应时间: {current:.3f}s")
        print("建议范围: 0.08s - 0.35s")
        print("参考值: 新手(0.25s), 中等(0.18s), 高手(0.12s)")
        
        new_value = float(input("请输入新的反应时间(秒): "))
        
        if 0.05 <= new_value <= 0.5:
            config_manager.update_config(human_reaction_time_base=new_value)
            print(f"✅ 反应时间已更新为: {new_value:.3f}s")
        else:
            print("❌ 值超出合理范围")
            
    except ValueError:
        print("❌ 请输入有效的数字")
    except Exception as e:
        print(f"❌ 修改失败: {e}")

def modify_skill_level():
    """修改技能水平"""
    try:
        from system_config import config_manager
        
        current = config_manager.config.human_skill_level
        print(f"\n当前技能水平: {current:.2f}")
        print("范围: 0.0 - 1.0")
        print("参考值: 新手(0.3), 中等(0.7), 高手(0.9)")
        
        new_value = float(input("请输入新的技能水平: "))
        
        if 0.0 <= new_value <= 1.0:
            config_manager.update_config(human_skill_level=new_value)
            print(f"✅ 技能水平已更新为: {new_value:.2f}")
        else:
            print("❌ 值必须在0.0-1.0之间")
            
    except ValueError:
        print("❌ 请输入有效的数字")
    except Exception as e:
        print(f"❌ 修改失败: {e}")

def modify_target_accuracy():
    """修改目标命中率"""
    try:
        from system_config import config_manager
        
        current = config_manager.config.target_accuracy
        print(f"\n当前目标命中率: {current:.1%}")
        print("建议范围: 10% - 40%")
        print("参考值: 保守(20%), 平衡(25%), 激进(30%)")
        
        new_value = float(input("请输入新的目标命中率(0.1-0.4): "))
        
        if 0.1 <= new_value <= 0.5:
            config_manager.update_config(target_accuracy=new_value)
            print(f"✅ 目标命中率已更新为: {new_value:.1%}")
        else:
            print("❌ 值必须在0.1-0.5之间")
            
    except ValueError:
        print("❌ 请输入有效的数字")
    except Exception as e:
        print(f"❌ 修改失败: {e}")

def preset_menu():
    """预设模式菜单"""
    preset_options = """
🎮 预设模式选择:

1. 🛡️ 保守模式 - 最大化隐蔽性 (推荐新手)
2. ⚖️ 平衡模式 - 性能与安全平衡 (推荐)
3. ⚡ 激进模式 - 更高性能 (高风险)
4. 🧪 测试模式 - 调试用途
5. 🔙 返回主菜单

请选择模式 (1-5): """
    
    choice = input(preset_options).strip()
    
    preset_map = {
        '1': 'conservative',
        '2': 'balanced', 
        '3': 'aggressive',
        '4': 'testing'
    }
    
    if choice in preset_map:
        apply_preset(preset_map[choice])
    elif choice == '5':
        return
    else:
        print("❌ 无效选择")

def apply_preset(preset_name):
    """应用预设配置"""
    try:
        from system_config import config_manager
        
        if config_manager.apply_profile(preset_name):
            print(f"✅ 已应用 {preset_name} 配置")
            
            # 显示配置详情
            config = config_manager.config
            print(f"反应时间: {config.human_reaction_time_base:.3f}s")
            print(f"目标命中率: {config.target_accuracy:.1%}")
            print(f"目标爆头率: {config.target_headshot_rate:.1%}")
        else:
            print(f"❌ 应用 {preset_name} 配置失败")
            
    except Exception as e:
        print(f"❌ 应用预设失败: {e}")

def save_config():
    """保存配置"""
    try:
        from system_config import config_manager
        
        if config_manager.save_config():
            print("✅ 配置已保存")
        else:
            print("❌ 配置保存失败")
            
    except Exception as e:
        print(f"❌ 保存配置失败: {e}")

def reset_config():
    """重置配置"""
    confirm = input("⚠️ 确定要重置为默认配置吗? (y/N): ").strip().lower()
    
    if confirm == 'y':
        try:
            from system_config import config_manager
            
            if config_manager.reset_to_defaults():
                print("✅ 配置已重置为默认值")
            else:
                print("❌ 重置配置失败")
                
        except Exception as e:
            print(f"❌ 重置配置失败: {e}")
    else:
        print("❌ 操作已取消")

def run_test():
    """运行测试"""
    print("🧪 启动系统增强测试...")
    
    try:
        # 导入并运行测试
        from system_test import run_comprehensive_test
        run_comprehensive_test()
        
    except ImportError:
        print("❌ 测试模块未找到")
    except Exception as e:
        print(f"❌ 测试运行失败: {e}")

def start_main_program():
    """启动主程序"""
    print("🚀 启动 Axiom 主程序...")
    
    try:
        # 检查主程序文件
        if not os.path.exists('main.py'):
            print("❌ 未找到 main.py 文件")
            return
        
        print("正在启动主程序，请稍候...")
        
        # 启动主程序
        import subprocess
        subprocess.run([sys.executable, 'main.py'])
        
    except Exception as e:
        print(f"❌ 启动主程序失败: {e}")

def main():
    """主函数"""
    print_banner()
    
    # 检查依赖
    if not check_dependencies():
        input("\n按回车键退出...")
        return
    
    # 初始化系统增强
    try:
        from behavior_simulator import behavior_manager
        behavior_manager.initialize()
        print("✅ 系统增强初始化完成\n")
    except Exception as e:
        print(f"❌ 系统增强初始化失败: {e}\n")
    
    # 主循环
    while True:
        try:
            choice = show_menu()
            
            if choice == '1':
                show_system_status()
            elif choice == '2':
                config_menu()
            elif choice == '3':
                preset_menu()
            elif choice == '4':
                print("📈 性能监控功能请使用GUI界面")
            elif choice == '5':
                run_test()
            elif choice == '6':
                start_main_program()
            elif choice == '7':
                print("👋 再见！")
                break
            else:
                print("❌ 无效选择，请重试")
            
            input("\n按回车键继续...")
            
        except KeyboardInterrupt:
            print("\n\n👋 程序被用户中断，再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")
            input("按回车键继续...")

if __name__ == "__main__":
    main()
