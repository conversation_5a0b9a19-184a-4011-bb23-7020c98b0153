# advanced_settings.py - 高级设置GUI控制面板
# 提供系统增强参数调整、状态监控、性能报告等功能

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from typing import Optional

from behavior_simulator import behavior_manager
from system_config import config_manager
from system_monitor import system_monitor
from language_manager import get_text

class AdvancedSettingsPanel:
    """高级设置控制面板"""
    
    def __init__(self, parent_window=None):
        self.parent = parent_window
        self.window = None
        self.config = config_manager.config
        
        # UI变量
        self.status_vars = {}
        self.config_vars = {}
        
        # 更新线程
        self.update_thread = None
        self.update_running = False
        
    def show_panel(self):
        """显示高级设置控制面板"""
        if self.window and self.window.winfo_exists():
            self.window.lift()
            return
            
        self.create_window()
        
    def create_window(self):
        """创建窗口"""
        self.window = tk.Toplevel(self.parent) if self.parent else tk.Tk()
        self.window.title("🛡️ Axiom 高级设置控制面板")
        self.window.geometry("800x600")
        self.window.resizable(True, True)
        
        # 设置窗口样式
        self.setup_styles()
        
        # 创建主要布局
        self.create_main_layout()
        
        # 初始化数据
        self.load_config_to_ui()
        
        # 启动状态更新
        self.start_status_update()
        
        # 绑定关闭事件
        self.window.protocol("WM_DELETE_WINDOW", self.on_close)
        
    def setup_styles(self):
        """设置UI样式"""
        style = ttk.Style()
        
        # 配置样式
        style.configure("Title.TLabel", font=("Arial", 12, "bold"))
        style.configure("Status.TLabel", font=("Arial", 10))
        style.configure("Warning.TLabel", font=("Arial", 10), foreground="red")
        style.configure("Success.TLabel", font=("Arial", 10), foreground="green")
        
    def create_main_layout(self):
        """创建主要布局"""
        # 创建笔记本控件
        notebook = ttk.Notebook(self.window)
        notebook.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 状态监控标签页
        self.create_status_tab(notebook)
        
        # 配置设置标签页
        self.create_config_tab(notebook)
        
        # 性能报告标签页
        self.create_performance_tab(notebook)
        
        # 预设配置标签页
        self.create_presets_tab(notebook)
        
    def create_status_tab(self, notebook):
        """创建状态监控标签页"""
        status_frame = ttk.Frame(notebook)
        notebook.add(status_frame, text="📊 状态监控")
        
        # 系统状态区域
        status_group = ttk.LabelFrame(status_frame, text="系统状态", padding=10)
        status_group.pack(fill="x", padx=5, pady=5)
        
        # 状态显示变量
        self.status_vars = {
            'enabled': tk.StringVar(value="检查中..."),
            'profile': tk.StringVar(value="检查中..."),
            'reaction_time': tk.StringVar(value="检查中..."),
            'skill_level': tk.StringVar(value="检查中..."),
            'fatigue': tk.StringVar(value="检查中..."),
            'stress': tk.StringVar(value="检查中..."),
            'accuracy': tk.StringVar(value="检查中..."),
            'headshot_rate': tk.StringVar(value="检查中..."),
            'process_disguised': tk.StringVar(value="检查中...")
        }
        
        # 创建状态显示
        row = 0
        ttk.Label(status_group, text="系统增强:", style="Title.TLabel").grid(row=row, column=0, sticky="w", pady=2)
        ttk.Label(status_group, textvariable=self.status_vars['enabled'], style="Status.TLabel").grid(row=row, column=1, sticky="w", padx=10)
        
        row += 1
        ttk.Label(status_group, text="当前配置:", style="Title.TLabel").grid(row=row, column=0, sticky="w", pady=2)
        ttk.Label(status_group, textvariable=self.status_vars['profile'], style="Status.TLabel").grid(row=row, column=1, sticky="w", padx=10)
        
        row += 1
        ttk.Label(status_group, text="反应时间:", style="Title.TLabel").grid(row=row, column=0, sticky="w", pady=2)
        ttk.Label(status_group, textvariable=self.status_vars['reaction_time'], style="Status.TLabel").grid(row=row, column=1, sticky="w", padx=10)
        
        row += 1
        ttk.Label(status_group, text="技能水平:", style="Title.TLabel").grid(row=row, column=0, sticky="w", pady=2)
        ttk.Label(status_group, textvariable=self.status_vars['skill_level'], style="Status.TLabel").grid(row=row, column=1, sticky="w", padx=10)
        
        row += 1
        ttk.Label(status_group, text="疲劳度:", style="Title.TLabel").grid(row=row, column=0, sticky="w", pady=2)
        ttk.Label(status_group, textvariable=self.status_vars['fatigue'], style="Status.TLabel").grid(row=row, column=1, sticky="w", padx=10)
        
        row += 1
        ttk.Label(status_group, text="压力度:", style="Title.TLabel").grid(row=row, column=0, sticky="w", pady=2)
        ttk.Label(status_group, textvariable=self.status_vars['stress'], style="Status.TLabel").grid(row=row, column=1, sticky="w", padx=10)
        
        # 性能统计区域
        perf_group = ttk.LabelFrame(status_frame, text="性能统计", padding=10)
        perf_group.pack(fill="x", padx=5, pady=5)
        
        row = 0
        ttk.Label(perf_group, text="当前命中率:", style="Title.TLabel").grid(row=row, column=0, sticky="w", pady=2)
        ttk.Label(perf_group, textvariable=self.status_vars['accuracy'], style="Status.TLabel").grid(row=row, column=1, sticky="w", padx=10)
        
        row += 1
        ttk.Label(perf_group, text="爆头率:", style="Title.TLabel").grid(row=row, column=0, sticky="w", pady=2)
        ttk.Label(perf_group, textvariable=self.status_vars['headshot_rate'], style="Status.TLabel").grid(row=row, column=1, sticky="w", padx=10)
        
        row += 1
        ttk.Label(perf_group, text="进程伪装:", style="Title.TLabel").grid(row=row, column=0, sticky="w", pady=2)
        ttk.Label(perf_group, textvariable=self.status_vars['process_disguised'], style="Status.TLabel").grid(row=row, column=1, sticky="w", padx=10)
        
        # 控制按钮区域
        control_group = ttk.LabelFrame(status_frame, text="系统控制", padding=10)
        control_group.pack(fill="x", padx=5, pady=5)
        
        button_frame = ttk.Frame(control_group)
        button_frame.pack(fill="x")
        
        ttk.Button(button_frame, text="🔄 刷新状态", command=self.refresh_status).pack(side="left", padx=5)
        ttk.Button(button_frame, text="📊 启动监控", command=self.start_monitoring).pack(side="left", padx=5)
        ttk.Button(button_frame, text="⏹️ 停止监控", command=self.stop_monitoring).pack(side="left", padx=5)
        ttk.Button(button_frame, text="📁 导出数据", command=self.export_data).pack(side="left", padx=5)
        
    def create_config_tab(self, notebook):
        """创建配置设置标签页"""
        config_frame = ttk.Frame(notebook)
        notebook.add(config_frame, text="⚙️ 参数设置")
        
        # 创建滚动区域
        canvas = tk.Canvas(config_frame)
        scrollbar = ttk.Scrollbar(config_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 人类行为设置
        human_group = ttk.LabelFrame(scrollable_frame, text="人类行为模拟", padding=10)
        human_group.pack(fill="x", padx=5, pady=5)
        
        self.config_vars = {
            'reaction_time': tk.DoubleVar(value=self.config.human_reaction_time_base),
            'skill_level': tk.DoubleVar(value=self.config.human_skill_level),
            'consistency': tk.DoubleVar(value=self.config.human_consistency),
            'target_accuracy': tk.DoubleVar(value=self.config.target_accuracy),
            'target_headshot_rate': tk.DoubleVar(value=self.config.target_headshot_rate),
            'mouse_jitter': tk.DoubleVar(value=self.config.mouse_jitter_max),
            'pid_randomness': tk.DoubleVar(value=self.config.pid_randomness)
        }
        
        # 创建配置控件
        self.create_config_controls(human_group)
        
        # 保存按钮
        save_frame = ttk.Frame(scrollable_frame)
        save_frame.pack(fill="x", padx=5, pady=10)
        
        ttk.Button(save_frame, text="💾 保存配置", command=self.save_config).pack(side="left", padx=5)
        ttk.Button(save_frame, text="🔄 重置默认", command=self.reset_config).pack(side="left", padx=5)
        ttk.Button(save_frame, text="✅ 应用配置", command=self.apply_config).pack(side="left", padx=5)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
    def create_config_controls(self, parent):
        """创建配置控件"""
        row = 0
        
        # 反应时间
        ttk.Label(parent, text="基础反应时间 (秒):").grid(row=row, column=0, sticky="w", pady=2)
        reaction_scale = ttk.Scale(parent, from_=0.08, to=0.35, variable=self.config_vars['reaction_time'], orient="horizontal")
        reaction_scale.grid(row=row, column=1, sticky="ew", padx=5)
        ttk.Label(parent, textvariable=self.config_vars['reaction_time']).grid(row=row, column=2, sticky="w")
        
        row += 1
        # 技能水平
        ttk.Label(parent, text="技能水平 (0-1):").grid(row=row, column=0, sticky="w", pady=2)
        skill_scale = ttk.Scale(parent, from_=0.3, to=1.0, variable=self.config_vars['skill_level'], orient="horizontal")
        skill_scale.grid(row=row, column=1, sticky="ew", padx=5)
        ttk.Label(parent, textvariable=self.config_vars['skill_level']).grid(row=row, column=2, sticky="w")
        
        row += 1
        # 操作一致性
        ttk.Label(parent, text="操作一致性 (0-1):").grid(row=row, column=0, sticky="w", pady=2)
        consistency_scale = ttk.Scale(parent, from_=0.5, to=0.95, variable=self.config_vars['consistency'], orient="horizontal")
        consistency_scale.grid(row=row, column=1, sticky="ew", padx=5)
        ttk.Label(parent, textvariable=self.config_vars['consistency']).grid(row=row, column=2, sticky="w")
        
        row += 1
        # 目标命中率
        ttk.Label(parent, text="目标命中率 (0-1):").grid(row=row, column=0, sticky="w", pady=2)
        accuracy_scale = ttk.Scale(parent, from_=0.1, to=0.5, variable=self.config_vars['target_accuracy'], orient="horizontal")
        accuracy_scale.grid(row=row, column=1, sticky="ew", padx=5)
        ttk.Label(parent, textvariable=self.config_vars['target_accuracy']).grid(row=row, column=2, sticky="w")
        
        row += 1
        # 目标爆头率
        ttk.Label(parent, text="目标爆头率 (0-1):").grid(row=row, column=0, sticky="w", pady=2)
        headshot_scale = ttk.Scale(parent, from_=0.05, to=0.3, variable=self.config_vars['target_headshot_rate'], orient="horizontal")
        headshot_scale.grid(row=row, column=1, sticky="ew", padx=5)
        ttk.Label(parent, textvariable=self.config_vars['target_headshot_rate']).grid(row=row, column=2, sticky="w")
        
        # 配置列宽
        parent.columnconfigure(1, weight=1)
        
    def create_performance_tab(self, notebook):
        """创建性能报告标签页"""
        perf_frame = ttk.Frame(notebook)
        notebook.add(perf_frame, text="📈 性能报告")
        
        # 性能报告文本区域
        text_frame = ttk.Frame(perf_frame)
        text_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        self.performance_text = tk.Text(text_frame, wrap="word", font=("Consolas", 10))
        perf_scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=self.performance_text.yview)
        self.performance_text.configure(yscrollcommand=perf_scrollbar.set)
        
        self.performance_text.pack(side="left", fill="both", expand=True)
        perf_scrollbar.pack(side="right", fill="y")
        
        # 控制按钮
        button_frame = ttk.Frame(perf_frame)
        button_frame.pack(fill="x", padx=10, pady=5)
        
        ttk.Button(button_frame, text="🔄 刷新报告", command=self.refresh_performance).pack(side="left", padx=5)
        ttk.Button(button_frame, text="📋 复制报告", command=self.copy_performance).pack(side="left", padx=5)
        ttk.Button(button_frame, text="🗑️ 清空报告", command=self.clear_performance).pack(side="left", padx=5)
        
    def create_presets_tab(self, notebook):
        """创建预设配置标签页"""
        presets_frame = ttk.Frame(notebook)
        notebook.add(presets_frame, text="🎛️ 预设配置")
        
        # 预设选择区域
        preset_group = ttk.LabelFrame(presets_frame, text="配置模式", padding=10)
        preset_group.pack(fill="x", padx=10, pady=10)
        
        # 预设按钮
        ttk.Button(preset_group, text="🛡️ 保守模式 (最安全)", 
                  command=lambda: self.apply_preset('conservative')).pack(fill="x", pady=2)
        ttk.Button(preset_group, text="⚖️ 平衡模式 (推荐)", 
                  command=lambda: self.apply_preset('balanced')).pack(fill="x", pady=2)
        ttk.Button(preset_group, text="⚡ 激进模式 (高风险)", 
                  command=lambda: self.apply_preset('aggressive')).pack(fill="x", pady=2)
        ttk.Button(preset_group, text="🧪 测试模式 (调试用)", 
                  command=lambda: self.apply_preset('testing')).pack(fill="x", pady=2)
        
        # 预设说明
        info_group = ttk.LabelFrame(presets_frame, text="模式说明", padding=10)
        info_group.pack(fill="both", expand=True, padx=10, pady=10)
        
        info_text = tk.Text(info_group, wrap="word", height=15, font=("Arial", 10))
        info_text.pack(fill="both", expand=True)
        
        info_content = """
🛡️ 保守模式 - 最大化隐蔽性
• 反应时间: 220ms
• 命中率: 20%
• 爆头率: 12%
• 适用: 高风险游戏 (如 Valorant)

⚖️ 平衡模式 - 性能与安全平衡
• 反应时间: 180ms
• 命中率: 25%
• 爆头率: 15%
• 适用: 大多数游戏 (推荐)

⚡ 激进模式 - 更高性能
• 反应时间: 150ms
• 命中率: 30%
• 爆头率: 20%
• 适用: 低风险环境

🧪 测试模式 - 调试和测试
• 启用详细日志
• 快速监控更新
• 用于参数调试
        """
        
        info_text.insert("1.0", info_content.strip())
        info_text.configure(state="disabled")

    def load_config_to_ui(self):
        """加载配置到UI"""
        try:
            self.config_vars['reaction_time'].set(self.config.human_reaction_time_base)
            self.config_vars['skill_level'].set(self.config.human_skill_level)
            self.config_vars['consistency'].set(self.config.human_consistency)
            self.config_vars['target_accuracy'].set(self.config.target_accuracy)
            self.config_vars['target_headshot_rate'].set(self.config.target_headshot_rate)
        except Exception as e:
            print(f"[高级设置GUI] 加载配置失败: {e}")

    def save_config(self):
        """保存配置"""
        try:
            # 更新配置对象
            self.config.human_reaction_time_base = self.config_vars['reaction_time'].get()
            self.config.human_skill_level = self.config_vars['skill_level'].get()
            self.config.human_consistency = self.config_vars['consistency'].get()
            self.config.target_accuracy = self.config_vars['target_accuracy'].get()
            self.config.target_headshot_rate = self.config_vars['target_headshot_rate'].get()

            # 保存到文件
            if config_manager.save_config():
                messagebox.showinfo("成功", "配置已保存")
            else:
                messagebox.showerror("错误", "配置保存失败")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置时出错: {e}")

    def apply_preset(self, preset_name):
        """应用预设配置"""
        try:
            if config_manager.apply_profile(preset_name):
                self.config = config_manager.config
                self.load_config_to_ui()
                messagebox.showinfo("成功", f"已应用 {preset_name} 配置")
            else:
                messagebox.showerror("错误", f"应用 {preset_name} 配置失败")
        except Exception as e:
            messagebox.showerror("错误", f"应用预设配置时出错: {e}")

    def start_status_update(self):
        """启动状态更新"""
        if not self.update_running:
            self.update_running = True
            self.update_thread = threading.Thread(target=self._status_update_loop, daemon=True)
            self.update_thread.start()

    def _status_update_loop(self):
        """状态更新循环"""
        while self.update_running and self.window and self.window.winfo_exists():
            try:
                self.update_status_display()
                time.sleep(2.0)  # 每2秒更新一次
            except Exception as e:
                print(f"[高级设置GUI] 状态更新错误: {e}")
                time.sleep(5.0)

    def update_status_display(self):
        """更新状态显示"""
        try:
            status = behavior_manager.get_status_report()

            # 更新状态变量
            self.status_vars['enabled'].set("✅ 启用" if status['enabled'] else "❌ 禁用")
            self.status_vars['process_disguised'].set("✅ 激活" if status['process_disguised'] else "❌ 未激活")

            human_profile = status.get('human_profile', {})
            self.status_vars['reaction_time'].set(f"{human_profile.get('reaction_time', 0):.3f}s")
            self.status_vars['skill_level'].set(f"{human_profile.get('skill_level', 0):.2f}")
            self.status_vars['fatigue'].set(f"{human_profile.get('fatigue_level', 0):.2f}")
            self.status_vars['stress'].set(f"{human_profile.get('stress_level', 0):.2f}")

            performance = status.get('performance', {})
            self.status_vars['accuracy'].set(f"{performance.get('accuracy', 0):.1%}")
            self.status_vars['headshot_rate'].set(f"{performance.get('headshot_rate', 0):.1%}")

        except Exception as e:
            print(f"[高级设置GUI] 更新状态显示失败: {e}")

    def refresh_status(self):
        """刷新状态"""
        self.update_status_display()
        messagebox.showinfo("完成", "状态已刷新")

    def start_monitoring(self):
        """启动监控"""
        system_monitor.start_monitoring()
        messagebox.showinfo("完成", "监控系统已启动")

    def stop_monitoring(self):
        """停止监控"""
        system_monitor.stop_monitoring()
        messagebox.showinfo("完成", "监控系统已停止")

    def export_data(self):
        """导出数据"""
        filename = system_monitor.export_data()
        if filename:
            messagebox.showinfo("完成", f"数据已导出到: {filename}")
        else:
            messagebox.showerror("错误", "数据导出失败")

    def refresh_performance(self):
        """刷新性能报告"""
        try:
            summary = system_monitor.get_performance_summary()
            recommendations = system_monitor.get_recommendations()

            report = "=== 系统增强性能报告 ===\n\n"
            report += f"会话时长: {summary.get('session_duration_minutes', 0):.1f} 分钟\n"
            report += f"总射击数: {summary.get('total_shots', 0)}\n"
            report += f"辅助率: {summary.get('assist_rate', 0):.1%}\n"
            report += f"失误率: {summary.get('miss_rate', 0):.1%}\n"
            report += f"平均命中率: {summary.get('avg_accuracy', 0):.1%}\n"
            report += f"平均反应时间: {summary.get('avg_reaction_time', 0):.3f}s\n"
            report += f"平均疲劳度: {summary.get('avg_fatigue_level', 0):.2f}\n"
            report += f"平均压力度: {summary.get('avg_stress_level', 0):.2f}\n"
            report += f"数据点数量: {summary.get('data_points_collected', 0)}\n"
            report += f"监控状态: {'运行中' if summary.get('monitoring_active', False) else '已停止'}\n\n"

            report += "=== 优化建议 ===\n"
            for i, rec in enumerate(recommendations, 1):
                report += f"{i}. {rec}\n"

            report += f"\n=== 报告生成时间 ===\n{time.strftime('%Y-%m-%d %H:%M:%S')}\n"

            self.performance_text.delete("1.0", tk.END)
            self.performance_text.insert("1.0", report)

        except Exception as e:
            messagebox.showerror("错误", f"刷新性能报告失败: {e}")

    def copy_performance(self):
        """复制性能报告"""
        try:
            content = self.performance_text.get("1.0", tk.END)
            self.window.clipboard_clear()
            self.window.clipboard_append(content)
            messagebox.showinfo("完成", "报告已复制到剪贴板")
        except Exception as e:
            messagebox.showerror("错误", f"复制失败: {e}")

    def clear_performance(self):
        """清空性能报告"""
        self.performance_text.delete("1.0", tk.END)

    def reset_config(self):
        """重置配置"""
        if messagebox.askyesno("确认", "确定要重置为默认配置吗？"):
            config_manager.reset_to_defaults()
            self.config = config_manager.config
            self.load_config_to_ui()
            messagebox.showinfo("完成", "配置已重置为默认值")

    def apply_config(self):
        """应用配置"""
        self.save_config()
        # 这里可以添加重新初始化系统增强的逻辑
        messagebox.showinfo("完成", "配置已应用，重启程序后生效")

    def on_close(self):
        """关闭窗口"""
        self.update_running = False
        if self.window:
            self.window.destroy()

# 全局实例
advanced_settings_panel = AdvancedSettingsPanel()

def show_advanced_settings_panel(parent=None):
    """显示高级设置控制面板"""
    global advanced_settings_panel
    if parent:
        advanced_settings_panel.parent = parent
    advanced_settings_panel.show_panel()
    return advanced_settings_panel
