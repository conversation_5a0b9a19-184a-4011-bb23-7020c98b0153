# performance_enhancer.py - 轻量级性能增强模块
# 专为性能敏感环境设计，最小化CPU开销

import random
import time
import math
import threading
from typing import Tuple, Optional
from dataclasses import dataclass

@dataclass
class LiteHumanProfile:
    """轻量级人类特征配置"""
    reaction_time_base: float = 0.18
    skill_level: float = 0.75
    miss_rate: float = 0.15  # 简化为固定失误率
    jitter_max: float = 1.0  # 最大抖动

class LiteHumanBehavior:
    """
    轻量级人类行为模拟器
    优化性能，减少计算复杂度
    """
    
    def __init__(self, profile: Optional[LiteHumanProfile] = None):
        self.profile = profile or LiteHumanProfile()
        
        # 预计算随机值缓存，减少实时计算
        self._reaction_cache = []
        self._jitter_cache = []
        self._miss_cache = []
        self._cache_size = 100
        self._cache_index = 0
        
        self._fill_caches()
        
        # 简化状态跟踪
        self.last_action_time = 0
        self.consecutive_hits = 0
    
    def _fill_caches(self):
        """预填充随机值缓存"""
        # 反应时间缓存
        for _ in range(self._cache_size):
            delay = random.normalvariate(
                self.profile.reaction_time_base, 
                self.profile.reaction_time_base * 0.2
            )
            self._reaction_cache.append(max(0.05, min(0.4, delay)))
        
        # 抖动缓存
        for _ in range(self._cache_size):
            jitter_x = random.uniform(-self.profile.jitter_max, self.profile.jitter_max)
            jitter_y = random.uniform(-self.profile.jitter_max, self.profile.jitter_max)
            self._jitter_cache.append((jitter_x, jitter_y))
        
        # 失误缓存
        for _ in range(self._cache_size):
            self._miss_cache.append(random.random() < self.profile.miss_rate)
    
    def get_reaction_delay(self) -> float:
        """获取反应延迟 - 优化版本"""
        delay = self._reaction_cache[self._cache_index % self._cache_size]
        self._cache_index += 1
        
        # 每100次调用重新填充缓存
        if self._cache_index >= self._cache_size:
            self._cache_index = 0
            if random.random() < 0.1:  # 10%概率重新生成
                self._fill_caches()
        
        return delay
    
    def should_miss(self) -> bool:
        """判断是否失误 - 简化版本"""
        # 连续命中过多时增加失误概率
        if self.consecutive_hits > 5:
            return True
        
        miss = self._miss_cache[self._cache_index % self._cache_size]
        return miss
    
    def get_aim_jitter(self) -> Tuple[float, float]:
        """获取瞄准抖动 - 缓存版本"""
        jitter = self._jitter_cache[self._cache_index % self._cache_size]
        return jitter
    
    def update_state(self, hit: bool):
        """更新状态 - 简化版本"""
        if hit:
            self.consecutive_hits += 1
        else:
            self.consecutive_hits = 0
        
        self.last_action_time = time.time()

class LitePerformanceController:
    """
    轻量级性能控制器
    使用简化算法减少计算开销
    """
    
    def __init__(self):
        self.target_accuracy = 0.25
        self.max_accuracy = 0.35
        
        # 使用滑动窗口而非完整历史
        self.recent_shots = []
        self.window_size = 20  # 只保留最近20发
        
        self.total_shots = 0
        self.total_hits = 0
    
    def should_assist(self) -> bool:
        """判断是否提供辅助 - 快速算法"""
        if len(self.recent_shots) < 5:
            return True  # 前几发正常辅助
        
        # 计算最近命中率
        recent_hits = sum(self.recent_shots)
        recent_accuracy = recent_hits / len(self.recent_shots)
        
        # 简单的线性控制
        if recent_accuracy > self.max_accuracy:
            return False  # 命中率过高，停止辅助
        elif recent_accuracy > self.target_accuracy:
            return random.random() < 0.6  # 60%概率辅助
        else:
            return True  # 正常辅助
    
    def record_shot(self, hit: bool):
        """记录射击结果 - 优化版本"""
        self.recent_shots.append(hit)
        
        # 保持窗口大小
        if len(self.recent_shots) > self.window_size:
            self.recent_shots.pop(0)
        
        # 更新总计数
        self.total_shots += 1
        if hit:
            self.total_hits += 1
    
    def get_current_accuracy(self) -> float:
        """获取当前命中率"""
        if self.total_shots == 0:
            return 0.0
        return self.total_hits / self.total_shots

class LiteMouseMovement:
    """
    轻量级鼠标移动
    使用简化算法生成自然移动
    """
    
    def __init__(self):
        self.last_move_time = 0
        self.movement_styles = ['direct', 'slight_curve', 'overshoot']
        self.style_weights = [0.6, 0.3, 0.1]
    
    def get_movement_offset(self, target_distance: float) -> Tuple[float, float]:
        """
        获取移动偏移 - 简化版本
        不生成完整轨迹，只计算最终偏移
        """
        current_time = time.time()
        
        # 限制计算频率
        if current_time - self.last_move_time < 0.016:  # 60fps限制
            return (0, 0)
        
        self.last_move_time = current_time
        
        # 选择移动风格
        style = random.choices(self.movement_styles, weights=self.style_weights)[0]
        
        if style == 'direct':
            # 直接移动，只加微小抖动
            jitter_x = random.uniform(-0.5, 0.5)
            jitter_y = random.uniform(-0.5, 0.5)
            return (jitter_x, jitter_y)
        
        elif style == 'slight_curve':
            # 轻微弯曲
            curve_factor = random.uniform(-2, 2)
            return (curve_factor, curve_factor * 0.5)
        
        else:  # overshoot
            # 轻微过冲
            overshoot = min(5, target_distance * 0.1)
            direction = random.choice([-1, 1])
            return (overshoot * direction, overshoot * direction * 0.3)

class LiteEnhancementManager:
    """
    轻量级增强管理器
    最小化性能开销
    """
    
    def __init__(self):
        self.enabled = True
        self.human_behavior = LiteHumanBehavior()
        self.performance_controller = LitePerformanceController()
        self.mouse_movement = LiteMouseMovement()
        
        # 性能监控
        self.last_performance_check = 0
        self.performance_check_interval = 5.0  # 5秒检查一次
        
        # 统计信息
        self.stats = {
            'total_calculations': 0,
            'avg_calculation_time': 0,
            'last_fps': 0
        }
    
    def should_assist_shot(self) -> bool:
        """判断是否辅助射击 - 快速版本"""
        if not self.enabled:
            return True
        
        start_time = time.perf_counter()
        
        # 快速判断
        should_assist = self.performance_controller.should_assist()
        should_miss = self.human_behavior.should_miss()
        
        result = should_assist and not should_miss
        
        # 性能统计
        calculation_time = time.perf_counter() - start_time
        self._update_performance_stats(calculation_time)
        
        return result
    
    def get_human_delay(self) -> float:
        """获取人类延迟 - 快速版本"""
        if not self.enabled:
            return 0.0
        
        return self.human_behavior.get_reaction_delay()
    
    def get_aim_adjustment(self, target_distance: float) -> Tuple[float, float]:
        """获取瞄准调整 - 快速版本"""
        if not self.enabled:
            return (0, 0)
        
        # 组合抖动和移动偏移
        jitter = self.human_behavior.get_aim_jitter()
        movement_offset = self.mouse_movement.get_movement_offset(target_distance)
        
        return (jitter[0] + movement_offset[0], jitter[1] + movement_offset[1])
    
    def record_shot_result(self, hit: bool):
        """记录射击结果"""
        if self.enabled:
            self.performance_controller.record_shot(hit)
            self.human_behavior.update_state(hit)
    
    def _update_performance_stats(self, calculation_time: float):
        """更新性能统计"""
        self.stats['total_calculations'] += 1
        
        # 计算平均计算时间
        if self.stats['avg_calculation_time'] == 0:
            self.stats['avg_calculation_time'] = calculation_time
        else:
            # 指数移动平均
            alpha = 0.1
            self.stats['avg_calculation_time'] = (
                alpha * calculation_time + 
                (1 - alpha) * self.stats['avg_calculation_time']
            )
    
    def get_performance_report(self) -> dict:
        """获取性能报告"""
        current_time = time.time()
        
        if current_time - self.last_performance_check > self.performance_check_interval:
            self.last_performance_check = current_time
            
            # 计算FPS影响
            avg_time_ms = self.stats['avg_calculation_time'] * 1000
            fps_impact = min(10, avg_time_ms * 60)  # 估算FPS影响
            
            return {
                'enabled': self.enabled,
                'avg_calculation_time_ms': avg_time_ms,
                'estimated_fps_impact': fps_impact,
                'total_calculations': self.stats['total_calculations'],
                'current_accuracy': self.performance_controller.get_current_accuracy(),
                'memory_usage_mb': self._estimate_memory_usage()
            }
        
        return {}
    
    def _estimate_memory_usage(self) -> float:
        """估算内存使用量"""
        # 简单估算
        base_usage = 2.0  # 基础2MB
        cache_usage = len(self.human_behavior._reaction_cache) * 0.001  # 缓存使用
        history_usage = len(self.performance_controller.recent_shots) * 0.001  # 历史数据
        
        return base_usage + cache_usage + history_usage
    
    def set_performance_mode(self, mode: str):
        """设置性能模式"""
        if mode == 'ultra_lite':
            # 超轻量模式
            self.human_behavior.profile.miss_rate = 0.1
            self.human_behavior._cache_size = 50
            self.performance_controller.window_size = 10
            self.performance_check_interval = 10.0
            
        elif mode == 'balanced':
            # 平衡模式
            self.human_behavior.profile.miss_rate = 0.15
            self.human_behavior._cache_size = 100
            self.performance_controller.window_size = 20
            self.performance_check_interval = 5.0
            
        elif mode == 'full_features':
            # 完整功能模式
            self.human_behavior.profile.miss_rate = 0.2
            self.human_behavior._cache_size = 200
            self.performance_controller.window_size = 50
            self.performance_check_interval = 2.0
        
        # 重新填充缓存
        self.human_behavior._fill_caches()
        print(f"[轻量增强] 已切换到 {mode} 模式")

# 创建轻量级全局实例
lite_enhancement = LiteEnhancementManager()

# 性能基准测试函数
def benchmark_performance(iterations: int = 1000):
    """性能基准测试"""
    print(f"🔬 开始性能基准测试 ({iterations} 次迭代)...")
    
    start_time = time.perf_counter()
    
    for i in range(iterations):
        # 模拟典型使用场景
        lite_enhancement.should_assist_shot()
        lite_enhancement.get_human_delay()
        lite_enhancement.get_aim_adjustment(100.0)
        lite_enhancement.record_shot_result(random.random() < 0.7)
    
    end_time = time.perf_counter()
    total_time = end_time - start_time
    
    avg_time_per_iteration = total_time / iterations
    estimated_fps_impact = avg_time_per_iteration * 60 * 1000  # ms per frame at 60fps
    
    print(f"📊 基准测试结果:")
    print(f"  总时间: {total_time:.3f}s")
    print(f"  平均每次: {avg_time_per_iteration*1000:.3f}ms")
    print(f"  估算FPS影响: {estimated_fps_impact:.2f}ms/frame")
    print(f"  内存使用: {lite_enhancement._estimate_memory_usage():.1f}MB")
    
    return {
        'total_time': total_time,
        'avg_time_per_iteration': avg_time_per_iteration,
        'estimated_fps_impact': estimated_fps_impact
    }

if __name__ == "__main__":
    # 运行基准测试
    benchmark_performance()
    
    # 测试不同性能模式
    for mode in ['ultra_lite', 'balanced', 'full_features']:
        print(f"\n测试 {mode} 模式:")
        lite_enhancement.set_performance_mode(mode)
        benchmark_performance(500)
