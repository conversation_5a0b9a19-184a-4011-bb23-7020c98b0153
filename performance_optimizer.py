# performance_optimizer.py - 性能优化器
# 根据硬件配置自动调整系统增强性能

import psutil
import platform
import time
import threading
from typing import Dict, Optional, Tuple

class HardwareDetector:
    """硬件检测器"""
    
    def __init__(self):
        self.cpu_info = self._detect_cpu()
        self.memory_info = self._detect_memory()
        self.gpu_info = self._detect_gpu()
        self.system_info = self._detect_system()
    
    def _detect_cpu(self) -> Dict:
        """检测CPU信息"""
        try:
            cpu_count = psutil.cpu_count(logical=False)  # 物理核心
            cpu_count_logical = psutil.cpu_count(logical=True)  # 逻辑核心
            cpu_freq = psutil.cpu_freq()
            
            return {
                'physical_cores': cpu_count,
                'logical_cores': cpu_count_logical,
                'base_frequency': cpu_freq.current if cpu_freq else 0,
                'max_frequency': cpu_freq.max if cpu_freq else 0,
                'brand': platform.processor()
            }
        except Exception as e:
            print(f"CPU检测失败: {e}")
            return {'physical_cores': 4, 'logical_cores': 8, 'base_frequency': 2400}
    
    def _detect_memory(self) -> Dict:
        """检测内存信息"""
        try:
            memory = psutil.virtual_memory()
            return {
                'total_gb': round(memory.total / (1024**3), 1),
                'available_gb': round(memory.available / (1024**3), 1),
                'usage_percent': memory.percent
            }
        except Exception as e:
            print(f"内存检测失败: {e}")
            return {'total_gb': 16, 'available_gb': 12, 'usage_percent': 25}
    
    def _detect_gpu(self) -> Dict:
        """检测GPU信息"""
        gpu_info = {'detected': False, 'name': 'Unknown', 'memory_gb': 0}
        
        try:
            # 性能优化：缓存subprocess导入
            if not hasattr(self, '_subprocess'):
                import subprocess
                self._subprocess = subprocess

            # 尝试检测NVIDIA GPU
            result = self._subprocess.run(['nvidia-smi', '--query-gpu=name,memory.total', '--format=csv,noheader,nounits'],
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                if lines and lines[0]:
                    parts = lines[0].split(', ')
                    gpu_info = {
                        'detected': True,
                        'name': parts[0].strip(),
                        'memory_gb': int(parts[1]) / 1024 if len(parts) > 1 else 0,
                        'type': 'NVIDIA'
                    }
        except Exception:
            pass
        
        # 如果没有检测到NVIDIA，尝试其他方法
        if not gpu_info['detected']:
            try:
                # 性能优化：缓存wmi导入和连接
                if not hasattr(self, '_wmi_connection'):
                    import wmi
                    self._wmi_connection = wmi.WMI()

                # 简单的GPU检测
                for gpu in self._wmi_connection.Win32_VideoController():
                    if gpu.Name and 'Intel' not in gpu.Name:
                        gpu_info = {
                            'detected': True,
                            'name': gpu.Name,
                            'memory_gb': 0,  # WMI通常不提供准确的显存信息
                            'type': 'Other'
                        }
                        break
            except Exception:
                pass
        
        return gpu_info
    
    def _detect_system(self) -> Dict:
        """检测系统信息"""
        return {
            'os': platform.system(),
            'os_version': platform.version(),
            'architecture': platform.architecture()[0],
            'python_version': platform.python_version()
        }
    
    def get_performance_score(self) -> int:
        """计算性能评分 (0-100)"""
        score = 0
        
        # CPU评分 (40分)
        cpu_cores = self.cpu_info.get('logical_cores', 4)
        cpu_freq = self.cpu_info.get('base_frequency', 2000)
        
        cpu_score = min(40, (cpu_cores * 3) + (cpu_freq / 100))
        score += cpu_score
        
        # 内存评分 (20分)
        memory_gb = self.memory_info.get('total_gb', 8)
        memory_score = min(20, memory_gb * 1.5)
        score += memory_score
        
        # GPU评分 (40分)
        if self.gpu_info.get('detected', False):
            gpu_name = self.gpu_info.get('name', '').lower()
            if 'rtx 3060' in gpu_name:
                gpu_score = 35
            elif 'rtx 30' in gpu_name:
                gpu_score = 30
            elif 'rtx 20' in gpu_name:
                gpu_score = 25
            elif 'gtx 16' in gpu_name:
                gpu_score = 20
            elif 'gtx' in gpu_name:
                gpu_score = 15
            else:
                gpu_score = 10
        else:
            gpu_score = 5
        
        score += gpu_score
        
        return min(100, int(score))

class PerformanceOptimizer:
    """性能优化器"""
    
    def __init__(self):
        self.hardware = HardwareDetector()
        self.performance_score = self.hardware.get_performance_score()
        self.current_config = None
        
        # 性能监控
        self.monitoring = False
        self.monitor_thread = None
        self.performance_history = []
        
        print(f"[性能优化] 硬件性能评分: {self.performance_score}/100")
        self._print_hardware_info()
    
    def _print_hardware_info(self):
        """打印硬件信息"""
        print(f"[硬件检测] CPU: {self.hardware.cpu_info.get('logical_cores', 'Unknown')} 核心")
        print(f"[硬件检测] 内存: {self.hardware.memory_info.get('total_gb', 'Unknown')} GB")
        
        if self.hardware.gpu_info.get('detected', False):
            gpu_name = self.hardware.gpu_info.get('name', 'Unknown')
            gpu_memory = self.hardware.gpu_info.get('memory_gb', 0)
            print(f"[硬件检测] GPU: {gpu_name}")
            if gpu_memory > 0:
                print(f"[硬件检测] 显存: {gpu_memory:.1f} GB")
        else:
            print(f"[硬件检测] GPU: 未检测到独立显卡")
    
    def get_recommended_config(self) -> Dict:
        """获取推荐配置"""
        if self.performance_score >= 80:
            # 高性能配置
            return {
                'mode': 'full_features',
                'anti_detection_level': 'high',
                'update_interval': 0.016,  # 60fps
                'cache_size': 200,
                'window_size': 50,
                'enable_advanced_features': True,
                'description': '高性能模式 - 完整反检测功能'
            }
        elif self.performance_score >= 60:
            # 平衡配置
            return {
                'mode': 'balanced',
                'anti_detection_level': 'medium',
                'update_interval': 0.020,  # 50fps
                'cache_size': 100,
                'window_size': 20,
                'enable_advanced_features': True,
                'description': '平衡模式 - 性能与功能兼顾'
            }
        elif self.performance_score >= 40:
            # 轻量配置
            return {
                'mode': 'lite',
                'anti_detection_level': 'low',
                'update_interval': 0.033,  # 30fps
                'cache_size': 50,
                'window_size': 10,
                'enable_advanced_features': False,
                'description': '轻量模式 - 优先保证性能'
            }
        else:
            # 超轻量配置
            return {
                'mode': 'ultra_lite',
                'anti_detection_level': 'minimal',
                'update_interval': 0.050,  # 20fps
                'cache_size': 25,
                'window_size': 5,
                'enable_advanced_features': False,
                'description': '超轻量模式 - 最小性能影响'
            }
    
    def is_rtx3060_compatible(self) -> Tuple[bool, str]:
        """检查RTX 3060兼容性"""
        gpu_name = self.hardware.gpu_info.get('name', '').lower()
        
        if 'rtx 3060' in gpu_name:
            # 检查是否为笔记本版本
            if 'laptop' in gpu_name or 'mobile' in gpu_name:
                return True, "RTX 3060 Laptop - 推荐使用平衡模式"
            else:
                return True, "RTX 3060 Desktop - 可使用高性能模式"
        
        elif 'rtx 30' in gpu_name:
            return True, f"检测到 {self.hardware.gpu_info.get('name')} - 兼容性良好"
        
        elif 'rtx' in gpu_name or 'gtx 16' in gpu_name:
            return True, f"检测到 {self.hardware.gpu_info.get('name')} - 建议使用轻量模式"
        
        else:
            return False, "未检测到兼容的NVIDIA GPU - 建议使用CPU模式"
    
    def apply_optimization(self):
        """应用性能优化"""
        config = self.get_recommended_config()
        self.current_config = config
        
        print(f"\n[性能优化] 应用配置: {config['description']}")
        print(f"[性能优化] 模式: {config['mode']}")
        print(f"[性能优化] 更新间隔: {config['update_interval']*1000:.1f}ms")
        print(f"[性能优化] 缓存大小: {config['cache_size']}")
        
        # 应用到轻量级系统增强
        try:
            from performance_enhancer import lite_enhancement
            lite_enhancement.set_performance_mode(config['mode'])

            # 调整具体参数
            lite_enhancement.human_behavior._cache_size = config['cache_size']
            lite_enhancement.performance_controller.window_size = config['window_size']
            lite_enhancement.performance_check_interval = config['update_interval'] * 100

            print(f"[性能优化] ✅ 轻量级系统增强配置已应用")

        except ImportError:
            print(f"[性能优化] ⚠️ 轻量级系统增强模块未找到")

        # 应用到完整系统增强
        try:
            from behavior_simulator import behavior_manager

            # 根据性能调整系统增强强度
            if config['anti_detection_level'] == 'minimal':
                behavior_manager.enabled = False
                print(f"[性能优化] 系统增强已禁用以节省性能")
            else:
                behavior_manager.enabled = True
                print(f"[性能优化] 系统增强强度: {config['anti_detection_level']}")

        except ImportError:
            print(f"[性能优化] ⚠️ 完整系统增强模块未找到")
        
        return config
    
    def start_monitoring(self):
        """开始性能监控"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        print(f"[性能监控] 已启动")
    
    def stop_monitoring(self):
        """停止性能监控"""
        self.monitoring = False
        print(f"[性能监控] 已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                # 收集性能数据
                cpu_percent = psutil.cpu_percent(interval=1)
                memory = psutil.virtual_memory()
                
                performance_data = {
                    'timestamp': time.time(),
                    'cpu_percent': cpu_percent,
                    'memory_percent': memory.percent,
                    'memory_available_gb': memory.available / (1024**3)
                }
                
                self.performance_history.append(performance_data)
                
                # 保持历史记录大小
                if len(self.performance_history) > 60:  # 保留1分钟数据
                    self.performance_history.pop(0)
                
                # 检查是否需要调整配置
                self._check_performance_adjustment(performance_data)
                
                time.sleep(1)
                
            except Exception as e:
                print(f"[性能监控] 错误: {e}")
                time.sleep(5)
    
    def _check_performance_adjustment(self, data: Dict):
        """检查是否需要调整性能"""
        if not self.current_config:
            return
        
        # 如果CPU使用率持续过高，降低反检测强度
        if data['cpu_percent'] > 85:
            if self.current_config['mode'] != 'ultra_lite':
                print(f"[性能监控] ⚠️ CPU使用率过高 ({data['cpu_percent']:.1f}%)，建议降低反检测强度")
        
        # 如果内存使用率过高，减少缓存
        if data['memory_percent'] > 90:
            print(f"[性能监控] ⚠️ 内存使用率过高 ({data['memory_percent']:.1f}%)，建议减少缓存大小")
    
    def get_performance_report(self) -> Dict:
        """获取性能报告"""
        if not self.performance_history:
            return {}
        
        recent_data = self.performance_history[-10:]  # 最近10秒
        
        avg_cpu = sum(d['cpu_percent'] for d in recent_data) / len(recent_data)
        avg_memory = sum(d['memory_percent'] for d in recent_data) / len(recent_data)
        
        return {
            'hardware_score': self.performance_score,
            'current_config': self.current_config,
            'avg_cpu_percent': avg_cpu,
            'avg_memory_percent': avg_memory,
            'rtx3060_compatible': self.is_rtx3060_compatible(),
            'recommendations': self._get_recommendations()
        }
    
    def _get_recommendations(self) -> list:
        """获取优化建议"""
        recommendations = []
        
        if not self.performance_history:
            return recommendations
        
        recent_data = self.performance_history[-5:]
        avg_cpu = sum(d['cpu_percent'] for d in recent_data) / len(recent_data)
        avg_memory = sum(d['memory_percent'] for d in recent_data) / len(recent_data)
        
        if avg_cpu > 80:
            recommendations.append("CPU使用率较高，建议降低反检测强度或关闭部分功能")
        
        if avg_memory > 85:
            recommendations.append("内存使用率较高，建议减少缓存大小")
        
        if self.performance_score < 50:
            recommendations.append("硬件性能较低，建议使用超轻量模式")
        
        gpu_compatible, gpu_msg = self.is_rtx3060_compatible()
        if gpu_compatible:
            recommendations.append(f"GPU兼容性: {gpu_msg}")
        else:
            recommendations.append("建议升级显卡以获得更好的AI推理性能")
        
        return recommendations

# 创建全局优化器实例
performance_optimizer = PerformanceOptimizer()

def auto_optimize_for_hardware():
    """自动为当前硬件优化配置"""
    print("🚀 开始自动硬件优化...")
    
    config = performance_optimizer.apply_optimization()
    
    # 检查RTX 3060兼容性
    compatible, message = performance_optimizer.is_rtx3060_compatible()
    print(f"\n[兼容性检查] {message}")
    
    if compatible:
        print("✅ 您的硬件配置适合运行AI推理和系统增强")
    else:
        print("⚠️ 建议检查硬件配置或使用CPU模式")
    
    return config

if __name__ == "__main__":
    # 运行自动优化
    auto_optimize_for_hardware()
    
    # 启动性能监控
    performance_optimizer.start_monitoring()
    
    # 等待一段时间后显示报告
    time.sleep(10)
    report = performance_optimizer.get_performance_report()
    
    print("\n📊 性能报告:")
    for key, value in report.items():
        print(f"  {key}: {value}")
    
    performance_optimizer.stop_monitoring()
