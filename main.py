# main.py - 主程序模块 (已集成系统增强)
import threading
import queue
import time
import math
import numpy as np
import cv2
import mss
import win32api
import win32con
import sys
import winsound
import os
import psutil
import random
import traceback
from typing import Optional, Tuple, List

# 根据模型类型导入不同函数库
import onnxruntime as ort
import torch
from ultralytics import YOLO

# 从我们自己建立的模块中导入
from config import Config, load_config
from win_utils import send_mouse_move, is_key_pressed, get_vk_name
from inference import preprocess_image, postprocess_outputs, non_max_suppression, PIDController
from overlay import start_pyqt_overlay, PyQtOverlay
from settings_gui import create_settings_gui
from status_panel import StatusPanel
from scaling_warning_dialog import check_windows_scaling

# ***** 系统增强模块导入 *****
from behavior_simulator import (
    behavior_manager,
    HumanBehaviorSimulator,
    AdaptiveScreenCapture,
    NaturalMouseMovement,
    PerformanceController
)

# 全局变量声明
ai_thread: Optional[threading.Thread] = None
auto_fire_thread: Optional[threading.Thread] = None

def optimize_cpu_performance(config):
    """优化CPU性能设置"""
    if not getattr(config, 'cpu_optimization', True):
        return

    try:
        # 获取当前进程
        current_process = psutil.Process()

        # 设置进程优先级 (Windows 专用)
        if sys.platform == "win32":
            process_priority = getattr(config, 'process_priority', 'high')
            try:
                if process_priority == 'realtime':
                    current_process.nice(psutil.REALTIME_PRIORITY_CLASS)
                    print("[性能优化] 设置进程优先级为：实时")
                elif process_priority == 'high':
                    current_process.nice(psutil.HIGH_PRIORITY_CLASS)
                    print("[性能优化] 设置进程优先级为：高")
                else:
                    current_process.nice(psutil.NORMAL_PRIORITY_CLASS)
                    print("[性能优化] 设置进程优先级为：正常")
            except Exception as e:
                print(f"[性能优化] 设置进程优先级失败：{e}")
        else:
            print("[性能优化] 非Windows系统，跳过进程优先级设置")

        # 设置CPU亲和性
        cpu_affinity = getattr(config, 'cpu_affinity', None)
        if cpu_affinity is not None:
            current_process.cpu_affinity(cpu_affinity)
            print(f"[性能优化] 设置CPU亲和性为：{cpu_affinity}")
        else:
            # 使用所有可用CPU核心
            all_cpus = list(range(psutil.cpu_count()))
            current_process.cpu_affinity(all_cpus)
            print(f"[性能优化] 使用所有CPU核心：{all_cpus}")

        # 设置线程优先级函数
        def set_thread_priority(thread_priority='high'):
            try:
                import win32process
                import win32api

                if thread_priority == 'realtime':
                    win32process.SetThreadPriority(win32api.GetCurrentThread(), win32process.THREAD_PRIORITY_TIME_CRITICAL)
                elif thread_priority == 'high':
                    win32process.SetThreadPriority(win32api.GetCurrentThread(), win32process.THREAD_PRIORITY_HIGHEST)
                else:
                    win32process.SetThreadPriority(win32api.GetCurrentThread(), win32process.THREAD_PRIORITY_NORMAL)
            except Exception as e:
                print(f"[性能优化] 设置线程优先级失败：{e}")

        # 返回线程优先级设置函数
        return set_thread_priority

    except Exception as e:
        print(f"[性能优化] CPU性能优化失败：{e}")
        return None

def optimize_onnx_session(config):
    """优化ONNX运行时设置"""
    try:
        # 设置ONNX运行时选项
        session_options = ort.SessionOptions()

        # 启用所有优化
        session_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL

        # 设置线程数量为CPU核心数
        session_options.intra_op_num_threads = psutil.cpu_count()
        session_options.inter_op_num_threads = psutil.cpu_count()

        # 启用并行执行
        session_options.execution_mode = ort.ExecutionMode.ORT_PARALLEL

        # 启用内存优化
        session_options.enable_mem_pattern = True
        session_options.enable_cpu_mem_arena = True

        print(f"[性能优化] ONNX使用 {psutil.cpu_count()} 个CPU核心")
        return session_options

    except Exception as e:
        print(f"[性能优化] ONNX优化失败：{e}")
        return None

def ai_logic_loop(config, model, model_type, boxes_queue, confidences_queue):
    """AI 推理和鼠标控制的主要循环 - 已集成反检测系统"""

    # ***** 系统增强：初始化增强组件 *****
    human_simulator = behavior_manager.human_behavior
    adaptive_capture = behavior_manager.screen_capture
    natural_mouse = behavior_manager.mouse_movement
    performance_ctrl = behavior_manager.performance_controller

    print("[系统增强] AI循环已启用系统增强功能")

    # 设置线程优先级 - 反检测：使用正常优先级
    set_thread_priority = optimize_cpu_performance(config)
    if set_thread_priority:
        # 反检测：强制使用正常优先级而非高优先级
        thread_priority = 'normal'  # 修改：不再使用高优先级
        set_thread_priority(thread_priority)
        print(f"[反检测] AI线程优先级设置为：{thread_priority} (反检测模式)")

    screen_capture = mss.mss()

    # 移除 CUDA 支持，PyTorch 模型只使用 CPU
    if model_type == 'pt':
        print(f"PyTorch 模型将在 CPU 上运行。")
        model.to('cpu')

    input_name = None
    if model_type == 'onnx':
        input_name = model.get_inputs()[0].name

    pid_x = PIDController(config.pid_kp_x, config.pid_ki_x, config.pid_kd_x)
    pid_y = PIDController(config.pid_kp_y, config.pid_ki_y, config.pid_kd_y)

    # 优化：缓存配置项，减少属性访问
    last_pid_update = 0
    pid_check_interval = 1.0  # 每秒检查一次PID参数变化

    # ***** 新增：音效提示相关变量 *****
    last_sound_time = 0
    sound_playing = False

    # 优化：预计算常用值
    half_width = config.width // 2
    half_height = config.height // 2

    # ***** 反检测：初始化状态变量 *****
    last_human_delay_time = 0
    consecutive_assists = 0  # 连续辅助计数
    last_movement_style = "smooth"  # 上次移动风格

    # 性能优化：缓存常用配置项
    cached_fov_size = config.fov_size
    cached_aim_part = config.aim_part
    cached_head_height_ratio = config.head_height_ratio
    cached_single_target_mode = config.single_target_mode
    cached_enable_sound_alert = config.enable_sound_alert
    last_config_update = 0
    config_update_interval = 1.0  # 每秒更新一次配置缓存

    while config.Running:
        current_time = time.time()

        # ***** 反检测：人性化延迟处理 *****
        # 在每次循环开始时应用人类反应延迟
        if current_time - last_human_delay_time > 0.1:  # 每100ms检查一次
            human_delay = human_simulator.get_human_reaction_delay()
            if human_delay > 0.01:  # 只有显著延迟才应用
                time.sleep(human_delay)
                last_human_delay_time = current_time

        # 优化：降低PID参数检查频率
        if current_time - last_pid_update > pid_check_interval:
            pid_x.Kp, pid_x.Ki, pid_x.Kd = config.pid_kp_x, config.pid_ki_x, config.pid_kd_x
            pid_y.Kp, pid_y.Ki, pid_y.Kd = config.pid_kp_y, config.pid_ki_y, config.pid_kd_y
            last_pid_update = current_time

            # ***** 系统增强：动态更新鼠标移动方式 *****
            # 使用随机方法选择
            methods = ["mouse_event", "SendInput", "SetCursorPos"]
            random_method = random.choice(methods)
            config._last_mouse_move_method = random_method
            if hasattr(config, 'mouse_move_method'):
                config.mouse_move_method = random_method

        # 性能优化：定期更新配置缓存
        if current_time - last_config_update > config_update_interval:
            cached_fov_size = config.fov_size
            cached_aim_part = config.aim_part
            cached_head_height_ratio = config.head_height_ratio
            cached_single_target_mode = config.single_target_mode
            cached_enable_sound_alert = config.enable_sound_alert
            last_config_update = current_time

        # 优化：缓存十字准心位置计算
        if config.fov_follow_mouse:
            try:
                x, y = win32api.GetCursorPos()
                config.crosshairX, config.crosshairY = x, y
            except Exception:
                config.crosshairX, config.crosshairY = half_width, half_height
        else:
            config.crosshairX, config.crosshairY = half_width, half_height

        # 优化：一次性检查所有瞄准键
        is_aiming = any(is_key_pressed(k) for k in config.AimKeys)

        # ***** 反检测：判断是否应该提供辅助 *****
        should_assist = performance_ctrl.should_assist_this_shot() if is_aiming else True
        force_miss = performance_ctrl.should_force_miss() if is_aiming else False

        if not config.AimToggle or (not config.keep_detecting and not is_aiming):
            # 优化：快速清空队列
            try:
                # 使用更高效的队列清空方法
                with boxes_queue.mutex:
                    boxes_queue.queue.clear()
                with confidences_queue.mutex:
                    confidences_queue.queue.clear()
            except:
                # 回退到原方法
                try:
                    while not boxes_queue.empty():
                        boxes_queue.get_nowait()
                    while not confidences_queue.empty():
                        confidences_queue.get_nowait()
                except queue.Empty:
                    pass
            boxes_queue.put([])
            confidences_queue.put([])
            time.sleep(0.05)  # 优化：非活动时使用较长睡眠
            continue

        fov_size = cached_fov_size
        crosshair_x, crosshair_y = config.crosshairX, config.crosshairY

        # 修改：检测整个画面而不是只检测FOV区域
        region = {
            "left": 0,
            "top": 0,
            "width": config.width,
            "height": config.height,
        }

        try:
            game_frame = np.array(screen_capture.grab(region))
        except Exception:
            continue
        if game_frame.size == 0:
            continue

        # 优化：只在需要时进行颜色转换
        boxes, confidences = [], []

        if model_type == 'onnx':
            input_tensor = preprocess_image(game_frame, config.model_input_size)
            try:
                outputs = model.run(None, {input_name: input_tensor})
                boxes, confidences = postprocess_outputs(outputs, region['width'], region['height'], config.model_input_size, config.min_confidence)
                boxes, confidences = non_max_suppression(boxes, confidences)
            except Exception as e:
                print(f"ONNX 推理错误: {e}")
                continue
        elif model_type == 'pt':
            game_frame_rgb = cv2.cvtColor(game_frame, cv2.COLOR_BGRA2RGB)
            try:
                results = model(game_frame_rgb, verbose=False)
                high_conf_indices = results[0].boxes.conf >= config.min_confidence
                boxes = results[0].boxes.xyxy[high_conf_indices].cpu().numpy().tolist()
                confidences = results[0].boxes.conf[high_conf_indices].cpu().numpy().tolist()
            except Exception as e:
                print(f"PyTorch 推理错误: {e}")
                continue

        # 修改：由于检测整个画面，boxes已经是绝对坐标，不需要转换
        absolute_boxes = boxes[:]

        # 新增：FOV过滤逻辑 - 只保留与FOV框有交集的人物框
        # 改进：检测整个画面后，使用FOV框进行过滤
        # 只要人物框与FOV框有任何重叠就会被保留（不需要完全包含）
        if absolute_boxes:
            fov_half = fov_size // 2
            fov_left = crosshair_x - fov_half
            fov_top = crosshair_y - fov_half
            fov_right = crosshair_x + fov_half
            fov_bottom = crosshair_y + fov_half

            filtered_boxes = []
            filtered_confidences = []

            for i, box in enumerate(absolute_boxes):
                x1, y1, x2, y2 = box
                # 矩形交集检测：只要人物框有一点点碰到FOV框就算
                # 条件：人物框左边 < FOV右边 且 人物框右边 > FOV左边 且
                #       人物框上边 < FOV下边 且 人物框下边 > FOV上边
                if (x1 < fov_right and x2 > fov_left and
                    y1 < fov_bottom and y2 > fov_top):
                    filtered_boxes.append(box)
                    if i < len(confidences):
                        filtered_confidences.append(confidences[i])

            absolute_boxes = filtered_boxes
            confidences = filtered_confidences

        # ***** 新增：单目标模式 - 只保留离准心最近的一个目标 *****
        if cached_single_target_mode and absolute_boxes:
            crosshair_x, crosshair_y = config.crosshairX, config.crosshairY
            closest_box = None
            min_distance = float('inf')
            closest_confidence = 0

            for i, box in enumerate(absolute_boxes):
                abs_x1, abs_y1, abs_x2, abs_y2 = box
                # 计算边界框中心点距离准心的距离（使用平方距离避免开方运算）
                box_center_x = (abs_x1 + abs_x2) * 0.5
                box_center_y = (abs_y1 + abs_y2) * 0.5
                distance_squared = (box_center_x - crosshair_x)**2 + (box_center_y - crosshair_y)**2

                if distance_squared < min_distance:
                    min_distance = distance_squared
                    closest_box = box
                    closest_confidence = confidences[i] if i < len(confidences) else 0.5

            # 只保留最近的一个目标
            if closest_box:
                absolute_boxes = [closest_box]
                confidences = [closest_confidence]
            else:
                absolute_boxes = []
                confidences = []

        # ***** 新增：音效提示系统 - 检测准心是否在敌人框内 *****
        if not cached_single_target_mode or not absolute_boxes:
            crosshair_x, crosshair_y = config.crosshairX, config.crosshairY
        target_detected = False

        if cached_enable_sound_alert and absolute_boxes:
            for box in absolute_boxes:
                abs_x1, abs_y1, abs_x2, abs_y2 = box
                # 检查准心是否在敌人框内
                if abs_x1 <= crosshair_x <= abs_x2 and abs_y1 <= crosshair_y <= abs_y2:
                    target_detected = True
                    break

            # 音效播放逻辑
            if target_detected:
                # 检查音效间隔，避免过于频繁播放
                if current_time - last_sound_time >= config.sound_interval / 1000.0:
                    try:
                        # 异步播放音效，避免阻塞主线程
                        threading.Thread(
                            target=winsound.Beep,
                            args=(config.sound_frequency, config.sound_duration),
                            daemon=True
                        ).start()
                        last_sound_time = current_time
                    except Exception as e:
                        pass  # 忽略音效播放错误

        # ***** 反检测：人性化瞄准逻辑 *****
        if is_aiming and absolute_boxes and should_assist and not force_miss:
            # 优化：使用缓存的瞄准参数
            aim_part = cached_aim_part
            head_height_ratio = cached_head_height_ratio
            head_width_ratio = config.head_width_ratio
            body_width_ratio = config.body_width_ratio

            valid_targets = []
            for box in absolute_boxes:
                abs_x1, abs_y1, abs_x2, abs_y2 = box
                box_w, box_h = abs_x2 - abs_x1, abs_y2 - abs_y1
                box_center_x = abs_x1 + box_w * 0.5

                # 优化：统一的瞄准部位计算
                if aim_part == "head":
                    target_x = box_center_x
                    target_y = abs_y1 + box_h * head_height_ratio * 0.5
                else: # "body"
                    target_x = box_center_x
                    head_h = box_h * head_height_ratio
                    target_y = (abs_y1 + head_h + abs_y2) * 0.5

                # ***** 反检测：添加人性化瞄准偏移 *****
                dx_to_target = target_x - crosshair_x
                dy_to_target = target_y - crosshair_y
                distance_to_target = math.sqrt(dx_to_target*dx_to_target + dy_to_target*dy_to_target)
                aim_offset_x, aim_offset_y = human_simulator.get_aim_offset(distance_to_target)

                # 应用人性化偏移
                target_x += aim_offset_x
                target_y += aim_offset_y

                moveX = target_x - crosshair_x
                moveY = target_y - crosshair_y
                distance_squared = moveX*moveX + moveY*moveY  # 使用平方距离排序
                valid_targets.append((distance_squared, moveX, moveY, target_x, target_y))

            if valid_targets:
                valid_targets.sort(key=lambda x: x[0])
                _, errorX, errorY, final_target_x, final_target_y = valid_targets[0]

                # ***** 反检测：使用自然鼠标移动 *****
                current_pos = (crosshair_x, crosshair_y)
                target_pos = (final_target_x, final_target_y)

                # 获取当前移动风格
                movement_style = human_simulator.get_mouse_movement_style()

                # 生成人性化轨迹
                trajectory = natural_mouse.generate_human_trajectory(
                    current_pos, target_pos, movement_style
                )

                # 执行轨迹移动（简化版本 - 只移动到最终位置）
                if len(trajectory) > 0:
                    final_pos = trajectory[-1]
                    dx = final_pos[0] - crosshair_x
                    dy = final_pos[1] - crosshair_y

                    # ***** 系统增强：使用随机的鼠标移动方法 *****
                    methods = ["mouse_event", "SendInput", "SetCursorPos"]
                    current_method = random.choice(methods)

                    if abs(dx) > 1 or abs(dy) > 1:  # 只有显著移动才执行
                        send_mouse_move(int(dx), int(dy), method=current_method)
                        consecutive_assists += 1

                        # 记录射击结果（简化 - 假设命中）
                        performance_ctrl.record_shot(hit=True, headshot=(aim_part == "head"))

                # 更新人类行为状态
                human_simulator.update_session_state(action_success=True)
            else:
                pid_x.reset()
                pid_y.reset()
        elif is_aiming and force_miss:
            # ***** 反检测：强制失误逻辑 *****
            # 故意不提供辅助或提供错误辅助
            performance_ctrl.record_shot(hit=False)
            human_simulator.update_session_state(action_success=False)
            consecutive_assists = 0
        else:
            pid_x.reset()
            pid_y.reset()
            consecutive_assists = 0

        # 优化：高效的队列管理
        try:
            # 使用非阻塞方式放入数据，如果队列满则丢弃旧数据
            if boxes_queue.full():
                try:
                    boxes_queue.get_nowait()
                except queue.Empty:
                    pass
            if confidences_queue.full():
                try:
                    confidences_queue.get_nowait()
                except queue.Empty:
                    pass

            boxes_queue.put_nowait(absolute_boxes)
            confidences_queue.put_nowait(confidences)
        except queue.Full:
            # 队列满时跳过这一帧
            pass

        # 优化：使用配置的检测间隔，确保一致性
        # 在性能模式下进一步优化睡眠时间
        if getattr(config, 'performance_mode', False):
            # 极短睡眠时间以最大化CPU使用率
            sleep_time = max(config.detect_interval, 0.0001)  # 最小0.1ms
        else:
            sleep_time = config.detect_interval
        time.sleep(sleep_time)

def auto_fire_loop(config, boxes_queue):
    """自动开火功能的独立循环 - 修复按键更新问题"""
    # 设置线程优先级
    set_thread_priority = optimize_cpu_performance(config)
    if set_thread_priority:
        thread_priority = getattr(config, 'thread_priority', 'high')
        set_thread_priority(thread_priority)
        print(f"[性能优化] 自动开火线程优先级设置为：{thread_priority}")

    last_key_state = False
    delay_start_time = None
    last_fire_time = 0
    cached_boxes = []
    last_box_update = 0

    # 优化参数 - 根据检测间隔调整
    BOX_UPDATE_INTERVAL = 1 / 60  # ***** 修改：与主循环同步更新频率 *****

    # 修复：动态更新按键配置
    auto_fire_key = config.auto_fire_key
    auto_fire_key2 = getattr(config, 'auto_fire_key2', None)
    last_key_update = 0
    key_update_interval = 0.5  # 每0.5秒检查一次按键配置变化

    while config.Running:
        current_time = time.time()

        # 修复：定期更新按键配置，允许动态更改
        if current_time - last_key_update > key_update_interval:
            auto_fire_key = config.auto_fire_key
            auto_fire_key2 = getattr(config, 'auto_fire_key2', None)
            last_key_update = current_time

        # 使用更新后的按键配置
        key_state = is_key_pressed(auto_fire_key)
        if auto_fire_key2:
            key_state = key_state or is_key_pressed(auto_fire_key2)

        # 处理按键状态变化
        if key_state and not last_key_state:
            delay_start_time = current_time

        if key_state:
            # 检查开镜延迟
            if delay_start_time and (current_time - delay_start_time >= config.auto_fire_delay):
                # 检查射击冷却时间
                if current_time - last_fire_time >= config.auto_fire_interval:

                    # 优化：动态调整boxes更新频率
                    if current_time - last_box_update >= BOX_UPDATE_INTERVAL:
                        try:
                            # 修复：通过直接访问底层 deque 来「偷看」最新项目，而不是消耗它。
                            # 这样可以防止自动开火线程与UI绘图线程竞争框体数据，解决UI卡顿问题。
                            if not boxes_queue.empty():
                                cached_boxes = boxes_queue.queue[-1]
                                last_box_update = current_time
                        except IndexError:
                            # 在多线程环境下，即使检查过 not empty，队列仍可能在访问前变空。
                            # 这种情况下，我们什么都不做，继续使用旧的 cached_boxes。
                            pass

                    # 优化：预计算瞄准参数
                    if cached_boxes:
                        crosshair_x, crosshair_y = config.crosshairX, config.crosshairY
                        target_part = config.auto_fire_target_part
                        head_height_ratio = config.head_height_ratio
                        head_width_ratio = config.head_width_ratio
                        body_width_ratio = config.body_width_ratio

                        # 快速射击判断
                        should_fire = False
                        for box in cached_boxes:
                            x1, y1, x2, y2 = box
                            box_w, box_h = x2 - x1, y2 - y1
                            box_center_x = x1 + box_w * 0.5

                            # 优化：快速边界检查
                            if target_part == "head":
                                head_h = box_h * head_height_ratio
                                head_w = box_w * head_width_ratio
                                head_x1 = box_center_x - head_w * 0.5
                                head_x2 = box_center_x + head_w * 0.5
                                head_y2 = y1 + head_h
                                should_fire = (head_x1 <= crosshair_x <= head_x2 and y1 <= crosshair_y <= head_y2)
                            elif target_part == "body":
                                body_w = box_w * body_width_ratio
                                body_x1 = box_center_x - body_w * 0.5
                                body_x2 = box_center_x + body_w * 0.5
                                body_y1 = y1 + box_h * head_height_ratio
                                should_fire = (body_x1 <= crosshair_x <= body_x2 and body_y1 <= crosshair_y <= y2)
                            elif target_part == "both":
                                # 快速计算头部和身体区域
                                head_h = box_h * head_height_ratio
                                head_w = box_w * head_width_ratio
                                head_x1 = box_center_x - head_w * 0.5
                                head_x2 = box_center_x + head_w * 0.5

                                is_in_head = (head_x1 <= crosshair_x <= head_x2 and y1 <= crosshair_y <= y1 + head_h)

                                if not is_in_head:
                                    body_w = box_w * body_width_ratio
                                    body_x1 = box_center_x - body_w * 0.5
                                    body_x2 = box_center_x + body_w * 0.5
                                    body_y1 = y1 + head_h
                                    is_in_body = (body_x1 <= crosshair_x <= body_x2 and body_y1 <= crosshair_y <= y2)
                                    should_fire = is_in_body
                                else:
                                    should_fire = True

                            if should_fire:
                                # 快速射击
                                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
                                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
                                last_fire_time = current_time
                                break
        else:
            delay_start_time = None
            # 优化：按键未按下时清空缓存
            if cached_boxes:
                cached_boxes = []

        last_key_state = key_state

        # ***** 修改：将此循环的休眠时间固定为 1/60 秒，与主循环同步 *****
        time.sleep(1 / 60)

def aim_toggle_key_listener(config, update_gui_callback=None):
    """持续监听自动瞄准开关快捷键 - 优化版本"""
    # 设置线程优先级
    set_thread_priority = optimize_cpu_performance(config)
    if set_thread_priority:
        thread_priority = getattr(config, 'thread_priority', 'high')
        set_thread_priority(thread_priority)
        print(f"[性能优化] 快捷键监听线程优先级设置为：{thread_priority}")

    last_state = False
    key_code = getattr(config, 'aim_toggle_key', 0x78)  # 默认 F9 键

    # 获取按键名称
    key_name = get_vk_name(key_code)
    print(f"[快捷键监听] 开始监听快捷键: {key_name} (0x{key_code:02X})")
    print(f"[快捷键监听] 当前自动瞄准状态: {config.AimToggle}")
    print(f"[快捷键监听] 按下 {key_name} 来切换自动瞄准功能")

    # 优化：预计算睡眠时间
    performance_mode = getattr(config, 'performance_mode', False)
    sleep_interval = 0.01 if performance_mode else 0.03  # 性能模式下更频繁检查

    # 调试计数器
    debug_counter = 0

    while config.Running:
        try:
            # 重新获取快捷键设置（可能在 GUI 中被更改）
            current_key_code = getattr(config, 'aim_toggle_key', 0x78)
            if current_key_code != key_code:
                key_code = current_key_code
                key_name = get_vk_name(key_code)
                print(f"[快捷键监听] 快捷键已更新为: {key_name} (0x{key_code:02X})")

            # 检测按键状态
            state = bool(win32api.GetAsyncKeyState(key_code) & 0x8000)

            # 检测按键按下事件（从未按下到按下的转换）
            if state and not last_state:
                old_state = config.AimToggle
                config.AimToggle = not config.AimToggle
                print(f"[快捷键] ✓ 检测到 {key_name} 按下！自动瞄准: {old_state} → {config.AimToggle}")

                if update_gui_callback:
                    update_gui_callback(config.AimToggle)

            last_state = state

            # 每30秒输出一次状态报告
            debug_counter += 1
            if debug_counter % (30 * (1.0 / sleep_interval)) == 0:
                print(f"[快捷键监听] 运行中... 当前监听: {key_name}, 自动瞄准: {config.AimToggle}")

        except Exception as e:
            print(f"[快捷键监听] 错误: {e}")
            traceback.print_exc()

        # 优化：根据性能模式调整睡眠时间
        time.sleep(sleep_interval)

if __name__ == "__main__":
    # 在程序开始时检测 Windows 缩放比例
    print("[系统检测] 正在检测 Windows 缩放设置...")
    if not check_windows_scaling():
        print("[系统检测] 程序因缩放设置问题而退出")
        sys.exit(1)
    print("[系统检测] ✓ 缩放设置检测通过")

    # ***** 反检测：性能优化和初始化 *****
    print("[性能优化] 正在检测硬件配置...")
    try:
        from performance_optimizer import auto_optimize_for_hardware
        optimization_config = auto_optimize_for_hardware()
        print(f"[性能优化] ✅ 已应用 {optimization_config['description']}")
    except ImportError:
        print("[性能优化] ⚠️ 性能优化模块未找到，使用默认配置")
    except Exception as e:
        print(f"[性能优化] ❌ 优化失败: {e}")

    print("[系统增强] 正在初始化系统增强...")
    behavior_manager.initialize()

    config = Config()
    load_config(config)

    # 调试：显示载入的鼠标移动方式
    print(f"[配置载入] 鼠标移动方式: {getattr(config, 'mouse_move_method', 'mouse_event')}")

    # ***** 反检测：使用伪装的CPU性能设置 *****
    print("[反检测] 正在应用伪装的性能设置...")
    # 注释掉原来的高性能优化，使用反检测版本
    # optimize_cpu_performance(config)
    print("[反检测] ✓ 伪装性能设置完成")

    # 优化：使用配置中的队列大小设置
    max_queue_size = getattr(config, 'max_queue_size', 3)
    boxes_queue = queue.Queue(maxsize=max_queue_size)
    confidences_queue = queue.Queue(maxsize=max_queue_size)

    def start_ai_threads(model_path):
        """由 GUI 调用，载入模型并启动/重启 AI 线程"""
        global ai_thread, auto_fire_thread, config

        # 停止现有线程
        if ai_thread is not None and ai_thread.is_alive():
            config.Running = False
            ai_thread.join()
            if auto_fire_thread is not None:
                auto_fire_thread.join()

        config.Running = True

        model, model_type = None, ''
        if model_path.endswith('.onnx'):
            model_type = 'onnx'
            try:
                print(f"正在载入 ONNX 模型: {model_path}")
                providers = ['DmlExecutionProvider', 'CPUExecutionProvider']

                # 获取优化的会话选项
                session_options = optimize_onnx_session(config)
                if session_options:
                    model = ort.InferenceSession(model_path, providers=providers, sess_options=session_options)
                    print(f"[性能优化] ONNX 模型已使用优化设置载入")
                else:
                    model = ort.InferenceSession(model_path, providers=providers)

                config.current_provider = model.get_providers()[0]
                print(f"ONNX 模型载入成功，使用: {config.current_provider}")
            except Exception as e:
                print(f"载入 ONNX 模型失败: {e}"); return False
        elif model_path.endswith('.pt'):
            model_type = 'pt'
            try:
                print(f"正在载入 PyTorch 模型: {model_path}")
                model = YOLO(model_path)
                model(np.zeros((640, 640, 3), dtype=np.uint8), verbose=False) # 预热
                print("PyTorch 模型载入成功。")
            except Exception as e:
                print(f"载入 PyTorch 模型失败: {e}"); return False
        else:
            print(f"错误: 不支持的模型格式: {model_path}"); return False

        ai_thread = threading.Thread(target=ai_logic_loop, args=(config, model, model_type, boxes_queue, confidences_queue), daemon=True)
        auto_fire_thread = threading.Thread(target=auto_fire_loop, args=(config, boxes_queue), daemon=True)

        ai_thread.start()
        auto_fire_thread.start()
        print("AI 相关线程已启动。")
        return True

    # 启动设置 GUI
    settings_thread = threading.Thread(target=create_settings_gui, args=(config, start_ai_threads), daemon=True)
    settings_thread.start()

    # 确保配置完全载入后再启动快捷键监听
    print(f"[初始化] 配置载入完成，快捷键设置: {getattr(config, 'aim_toggle_key', 'None')}")
    time.sleep(0.5)  # 等待 GUI 完全初始化

    # 启动快捷键监听（在 GUI 启动后）
    toggle_thread = threading.Thread(target=aim_toggle_key_listener, args=(config,), daemon=True)
    toggle_thread.start()
    print("[初始化] 快捷键监听线程已启动")

    print("启动 Overlays... 所有模块已开始运作。")

    # ***** 系统增强：显示系统状态 *****
    if behavior_manager.enabled:
        status_report = behavior_manager.get_status_report()
        print("\n" + "="*50)
        print("🛡️ 系统增强状态报告")
        print("="*50)
        print(f"系统状态: {'✅ 启用' if status_report['enabled'] else '❌ 禁用'}")
        print(f"进程伪装: {'✅ 激活' if status_report['process_disguised'] else '❌ 未激活'}")

        human_profile = status_report.get('human_profile', {})
        print(f"反应时间: {human_profile.get('reaction_time', 0):.3f}s")
        print(f"技能水平: {human_profile.get('skill_level', 0):.2f}")
        print(f"疲劳度: {human_profile.get('fatigue_level', 0):.2f}")
        print(f"压力度: {human_profile.get('stress_level', 0):.2f}")

        performance = status_report.get('performance', {})
        print(f"当前命中率: {performance.get('accuracy', 0):.1%}")
        print(f"爆头率: {performance.get('headshot_rate', 0):.1%}")
        print("="*50)
        print("💡 提示: 可通过设置界面的 '🛡️ 高级设置' 按钮打开控制面板")
        print("="*50 + "\n")

    from PyQt6.QtWidgets import QApplication

    app = QApplication(sys.argv)

    # 建立并显示主要的绘图覆盖层 (人物框, FOV)
    main_overlay = PyQtOverlay(boxes_queue, confidences_queue, config)
    main_overlay.show()

    # 建立并显示新的状态面板
    status_panel = StatusPanel(config)
    status_panel.show()

    # 启动 PyQt 应用程序事件循环，这会管理所有 PyQt 窗口
    try:
        sys.exit(app.exec())
    finally:
        # ***** 系统增强：程序退出时清理系统 *****
        print("[系统增强] 正在清理系统增强...")
        behavior_manager.cleanup()
        print("[系统增强] ✓ 系统增强清理完成")