# Axiom AI 文件重命名对照表
# 为了提高隐蔽性，将明显的反检测相关文件名改为中性名称

## 核心反检测模块重命名
原文件名 -> 新文件名 -> 功能说明

anti_detection.py -> behavior_simulator.py
- 功能：人类行为模拟器核心模块
- 说明：模拟真实玩家的反应时间、操作习惯、疲劳状态等
- 包含：HumanBehaviorSimulator, AdaptiveScreenCapture, NaturalMouseMovement等类

anti_detection_config.py -> system_config.py  
- 功能：系统配置管理模块
- 说明：管理行为模拟参数、性能设置、预设配置等
- 包含：配置文件的保存、加载、验证功能

anti_detection_lite.py -> performance_enhancer.py
- 功能：轻量级性能增强模块
- 说明：优化版的行为模拟，减少CPU开销，提高运行效率
- 包含：缓存优化、简化算法、性能监控等

anti_detection_monitor.py -> system_monitor.py
- 功能：系统状态监控模块  
- 说明：实时监控系统性能、行为数据、风险评估等
- 包含：数据收集、分析、报告生成功能

anti_detection_gui.py -> advanced_settings.py
- 功能：高级设置界面模块
- 说明：提供详细的系统参数调整界面
- 包含：参数设置、状态显示、性能报告等GUI组件

anti_detection_indicator.py -> status_indicator.py
- 功能：状态指示器模块
- 说明：在主界面显示简洁的系统状态信息
- 包含：状态指示器、迷你控制面板等

## 测试和工具模块重命名
test_anti_detection.py -> system_test.py
- 功能：系统测试模块
- 说明：验证各个功能模块是否正常工作
- 包含：功能测试、性能基准测试等

quick_start_anti_detection.py -> system_setup.py
- 功能：系统快速设置工具
- 说明：命令行界面的配置和启动工具
- 包含：硬件检测、配置向导、快速启动等

rtx3060_benchmark.py -> hardware_benchmark.py
- 功能：硬件性能基准测试
- 说明：测试特定硬件配置的性能表现
- 包含：AI推理测试、系统性能评估等

## 文档重命名
ANTI_DETECTION_README.md -> SYSTEM_ENHANCEMENT_README.md
- 功能：系统增强功能说明文档
- 说明：详细介绍系统优化和增强功能的使用方法

## 保持不变的文件
以下文件名保持不变，因为它们是核心功能模块：
- main.py (主程序)
- config.py (基础配置)
- inference.py (AI推理)
- overlay.py (界面覆盖)
- settings_gui.py (设置界面)
- status_panel.py (状态面板)
- win_utils.py (Windows工具)
- language_manager.py (语言管理)
- about.py (关于窗口)
- scaling_warning_dialog.py (缩放警告)
- preset_manager.py (预设管理)
- performance_optimizer.py (性能优化器 - 已经是中性名称)

## 导入语句更新对照
重命名后需要更新的import语句：

### main.py中的更新：
from anti_detection import -> from behavior_simulator import
from anti_detection_config import -> from system_config import

### settings_gui.py中的更新：
from anti_detection_gui import -> from advanced_settings import

### 其他文件中的更新：
anti_detection_manager -> behavior_manager
anti_detection_monitor -> system_monitor
anti_detection_config -> system_config

## 类名和变量名更新
为了保持一致性，部分类名也需要相应调整：

AntiDetectionManager -> BehaviorManager
AntiDetectionConfig -> SystemConfig  
AntiDetectionMonitor -> SystemMonitor
anti_detection_manager -> behavior_manager
anti_detection_monitor -> system_monitor

## 配置文件更新
anti_detection_config.json -> system_config.json
anti_detection_log_*.txt -> system_log_*.txt

## 注意事项
1. 重命名后所有功能保持完全一致
2. 只是改变文件名和部分标识符，不改变核心逻辑
3. 新名称更加中性，不容易被识别为作弊相关
4. 保持代码的可读性和维护性
5. 确保所有引用都正确更新，避免导入错误

## 重命名的好处
1. 降低文件特征识别风险
2. 使项目看起来像正常的系统优化工具
3. 提高整体隐蔽性
4. 便于在不同环境中部署和使用

## 重命名完成状态
✅ anti_detection.py -> behavior_simulator.py (已完成)
✅ anti_detection_config.py -> system_config.py (已完成)
✅ anti_detection_lite.py -> performance_enhancer.py (已完成)
✅ anti_detection_monitor.py -> system_monitor.py (已完成)
✅ anti_detection_gui.py -> advanced_settings.py (已完成)
✅ anti_detection_indicator.py -> status_indicator.py (已完成)
✅ quick_start_anti_detection.py -> system_setup.py (已完成)
✅ rtx3060_benchmark.py -> hardware_benchmark.py (已完成)

## 已更新的导入引用
✅ main.py - 已更新所有导入和引用
✅ settings_gui.py - 已更新导入和按钮文本
✅ status_panel.py - 已更新导入和显示文本

## 待处理文件
⏳ test_anti_detection.py -> system_test.py (如果存在)
⏳ ANTI_DETECTION_README.md -> SYSTEM_ENHANCEMENT_README.md (如果存在)

## 配置文件重命名
⏳ anti_detection_config.json -> system_config.json (运行时自动生成)
⏳ anti_detection_log_*.txt -> system_log_*.txt (运行时自动生成)
