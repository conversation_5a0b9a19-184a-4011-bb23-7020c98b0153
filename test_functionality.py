#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
功能测试脚本 - 检测所有模块是否正常工作
"""

import sys
import traceback
import time

def test_imports():
    """测试所有模块导入"""
    print("🔍 测试模块导入...")
    
    modules_to_test = [
        'config',
        'win_utils', 
        'inference',
        'overlay',
        'settings_gui',
        'status_panel',
        'scaling_warning_dialog',
        'behavior_simulator',
        'system_config',
        'performance_optimizer',
        'advanced_settings',
        'system_monitor',
        'language_manager',
        'about',
        'preset_manager'
    ]
    
    success_count = 0
    failed_modules = []
    
    for module in modules_to_test:
        try:
            __import__(module)
            print(f"  ✅ {module}")
            success_count += 1
        except ImportError as e:
            print(f"  ❌ {module} - 导入失败: {e}")
            failed_modules.append(module)
        except Exception as e:
            print(f"  ⚠️ {module} - 其他错误: {e}")
            failed_modules.append(module)
    
    print(f"\n📊 导入测试结果: {success_count}/{len(modules_to_test)} 成功")
    
    if failed_modules:
        print(f"❌ 失败的模块: {', '.join(failed_modules)}")
        return False
    else:
        print("✅ 所有模块导入成功！")
        return True

def test_config_loading():
    """测试配置加载"""
    print("\n🔧 测试配置加载...")
    
    try:
        from config import Config, load_config
        config = Config()
        load_config(config)
        print("  ✅ 基础配置加载成功")
        
        # 检查关键配置项
        required_attrs = ['fov_size', 'AimKeys', 'model_path', 'min_confidence']
        for attr in required_attrs:
            if hasattr(config, attr):
                print(f"  ✅ {attr}: {getattr(config, attr)}")
            else:
                print(f"  ❌ 缺少配置项: {attr}")
                return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ 配置加载失败: {e}")
        traceback.print_exc()
        return False

def test_behavior_simulator():
    """测试行为模拟器"""
    print("\n🤖 测试行为模拟器...")
    
    try:
        from behavior_simulator import behavior_manager, PerformanceController
        
        # 测试行为管理器
        print(f"  ✅ 行为管理器状态: {'启用' if behavior_manager.enabled else '禁用'}")
        
        # 测试性能控制器
        perf_ctrl = PerformanceController()
        
        # 测试关键方法
        if hasattr(perf_ctrl, 'should_assist_this_shot'):
            result = perf_ctrl.should_assist_this_shot()
            print(f"  ✅ should_assist_this_shot(): {result}")
        else:
            print("  ❌ 缺少 should_assist_this_shot 方法")
            return False
            
        if hasattr(perf_ctrl, 'should_force_miss'):
            result = perf_ctrl.should_force_miss()
            print(f"  ✅ should_force_miss(): {result}")
        else:
            print("  ❌ 缺少 should_force_miss 方法")
            return False
        
        # 测试记录射击
        perf_ctrl.record_shot(hit=True, headshot=False)
        stats = perf_ctrl.get_performance_stats()
        print(f"  ✅ 性能统计: 命中率 {stats['accuracy']:.1%}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 行为模拟器测试失败: {e}")
        traceback.print_exc()
        return False

def test_mouse_movement():
    """测试鼠标移动功能"""
    print("\n🖱️ 测试鼠标移动...")
    
    try:
        from win_utils import send_mouse_move
        
        # 测试不同的移动方法
        methods = ["mouse_event", "SendInput", "SetCursorPos"]
        
        for method in methods:
            try:
                # 测试小幅移动（不会实际移动鼠标）
                send_mouse_move(0, 0, method=method)
                print(f"  ✅ {method} 方法可用")
            except Exception as e:
                print(f"  ❌ {method} 方法失败: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ 鼠标移动测试失败: {e}")
        traceback.print_exc()
        return False

def test_model_loading():
    """测试模型加载"""
    print("\n🧠 测试模型加载...")
    
    try:
        import os
        from config import Config
        
        config = Config()
        model_path = getattr(config, 'model_path', '模型\\Roblox.onnx')
        
        if os.path.exists(model_path):
            print(f"  ✅ 模型文件存在: {model_path}")
            
            # 测试ONNX模型加载
            if model_path.endswith('.onnx'):
                try:
                    import onnxruntime as ort
                    session = ort.InferenceSession(model_path, providers=['CPUExecutionProvider'])
                    print(f"  ✅ ONNX模型加载成功")
                    return True
                except Exception as e:
                    print(f"  ❌ ONNX模型加载失败: {e}")
                    return False
            
            # 测试PyTorch模型加载
            elif model_path.endswith('.pt'):
                try:
                    from ultralytics import YOLO
                    model = YOLO(model_path)
                    print(f"  ✅ PyTorch模型加载成功")
                    return True
                except Exception as e:
                    print(f"  ❌ PyTorch模型加载失败: {e}")
                    return False
        else:
            print(f"  ❌ 模型文件不存在: {model_path}")
            return False
            
    except Exception as e:
        print(f"  ❌ 模型加载测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 Axiom AI 功能测试开始")
    print("=" * 50)
    
    test_results = []
    
    # 运行所有测试
    test_results.append(("模块导入", test_imports()))
    test_results.append(("配置加载", test_config_loading()))
    test_results.append(("行为模拟器", test_behavior_simulator()))
    test_results.append(("鼠标移动", test_mouse_movement()))
    test_results.append(("模型加载", test_model_loading()))
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:12} : {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有功能测试通过！程序应该可以正常运行。")
        return True
    else:
        print("⚠️ 部分功能存在问题，请检查上述失败的测试项。")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生未预期的错误: {e}")
        traceback.print_exc()
        sys.exit(1)
