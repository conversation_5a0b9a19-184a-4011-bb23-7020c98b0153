# Axiom AI 反检测系统说明

## 🛡️ 系统概述

本反检测系统是对Axiom AI自瞄项目的重大升级，通过多层次的技术手段大幅降低被反作弊系统检测的概率。系统采用人性化行为模拟、API调用混淆、进程特征伪装等先进技术。

## 🚀 核心特性

### 1. 人性化行为模拟 (P0优先级)
- **反应时间模拟**: 基于正态分布的人类反应延迟
- **技能水平建模**: 可配置的玩家技能等级
- **疲劳和压力系统**: 模拟长时间游戏的状态变化
- **故意失误机制**: 智能控制失误率保持真实性
- **瞄准偏移**: 模拟人类不完美的瞄准

### 2. 自适应截图优化 (P1优先级)
- **动态频率调整**: 根据游戏状态调整截图频率
- **智能ROI**: 只截图必要区域减少API调用
- **随机抖动**: 避免规律性截图模式

### 3. 自然鼠标移动 (P0优先级)
- **贝塞尔曲线轨迹**: 生成自然的移动路径
- **多种移动风格**: 平滑、抖动、过冲、欠冲
- **微观抖动**: 模拟人类手部微颤
- **随机延迟**: 模拟神经传导延迟

### 4. 性能统计控制 (P0优先级)
- **命中率控制**: 智能维持合理的命中率
- **爆头率管理**: 避免异常的爆头表现
- **强制失误**: 主动制造失误保持真实性
- **KD比控制**: 维持正常的击杀死亡比

### 5. API调用混淆 (P1优先级)
- **方法随机化**: 随机选择鼠标移动API
- **调用模式打乱**: 避免固定的调用序列
- **参数变化**: 动态调整API参数

### 6. 进程特征伪装 (P2优先级)
- **优先级伪装**: 使用正常进程优先级
- **CPU使用模拟**: 模拟正常软件的资源占用
- **内存访问优化**: 减少异常的内存操作

## 📁 文件结构

```
├── anti_detection.py              # 反检测核心模块
├── anti_detection_config.py       # 配置管理
├── anti_detection_monitor.py      # 状态监控
├── test_anti_detection.py         # 测试脚本
├── main.py                        # 主程序 (已集成)
├── config.py                      # 配置文件 (已更新)
├── inference.py                   # 推理模块 (已优化)
├── win_utils.py                   # 工具函数 (已增强)
└── ANTI_DETECTION_README.md       # 本说明文件
```

## ⚙️ 配置说明

### 预设配置文件

1. **保守模式** (`conservative`)
   - 最大化隐蔽性，最低检测风险
   - 反应时间: 220ms，命中率: 20%
   - 适合高风险游戏环境

2. **平衡模式** (`balanced`) - 默认
   - 性能与隐蔽性平衡
   - 反应时间: 180ms，命中率: 25%
   - 适合大多数情况

3. **激进模式** (`aggressive`)
   - 更高性能但风险较大
   - 反应时间: 150ms，命中率: 30%
   - 适合低风险环境

4. **测试模式** (`testing`)
   - 用于调试和测试
   - 启用详细日志和监控

### 关键参数

```python
# 人类行为参数
human_reaction_time_base = 0.18    # 基础反应时间(秒)
human_skill_level = 0.75           # 技能水平 (0-1)
target_accuracy = 0.25             # 目标命中率
target_headshot_rate = 0.15        # 目标爆头率

# 控制参数
force_miss_threshold = 0.4         # 强制失误阈值
mouse_jitter_max = 0.5            # 鼠标抖动范围
pid_randomness = 0.1              # PID随机性
```

## 🔧 使用方法

### 1. 基本使用

```python
# 系统会自动初始化，无需手动配置
# 在main.py中已集成所有功能
```

### 2. 配置管理

```python
from anti_detection_config import config_manager

# 加载配置
config = config_manager.load_config()

# 应用预设配置
config_manager.apply_profile('conservative')  # 保守模式
config_manager.apply_profile('balanced')      # 平衡模式
config_manager.apply_profile('aggressive')    # 激进模式

# 自定义配置
config_manager.update_config(
    human_reaction_time_base=0.2,
    target_accuracy=0.22
)
```

### 3. 监控系统

```python
from anti_detection_monitor import anti_detection_monitor

# 启动监控
anti_detection_monitor.start_monitoring()

# 获取性能报告
summary = anti_detection_monitor.get_performance_summary()
print(summary)

# 获取优化建议
recommendations = anti_detection_monitor.get_recommendations()
for rec in recommendations:
    print(f"建议: {rec}")

# 导出数据
anti_detection_monitor.export_data("performance_log.txt")
```

### 4. 测试验证

```bash
# 运行测试脚本验证功能
python test_anti_detection.py
```

## 📊 监控指标

系统会实时监控以下指标并提供警告：

- **命中率**: 超过40%时警告
- **爆头率**: 超过30%时警告
- **反应时间**: 低于100ms时警告
- **辅助率**: 超过90%时警告
- **统计异常**: 检测不自然的数据模式

## ⚠️ 重要提醒

### 安全建议

1. **首次使用**: 建议使用保守模式
2. **参数调整**: 根据监控建议逐步优化
3. **定期检查**: 查看性能报告和警告
4. **数据导出**: 定期备份监控数据

### 风险评估

| 游戏 | 建议模式 | 风险等级 | 说明 |
|------|----------|----------|------|
| Valorant | 保守模式 | 🔴 高 | Vanguard检测极强 |
| CS2 | 平衡模式 | 🟡 中 | VAC相对温和 |
| Apex Legends | 平衡模式 | 🟡 中 | EAC中等强度 |
| 其他游戏 | 激进模式 | 🟢 低 | 根据具体情况 |

### 法律声明

**本系统仅用于技术研究和学习目的**

- ❌ 不得用于实际游戏中
- ❌ 违反游戏服务条款
- ❌ 可能导致账号封禁
- ✅ 适用于反作弊研究
- ✅ 适用于AI技术学习
- ✅ 适用于网络安全研究

## 🔬 技术原理

### 检测对抗机制

1. **行为模式对抗**
   - 统计学习模拟真实玩家行为
   - 动态调整参数避免机械化模式
   - 多维度数据融合提高真实性

2. **API调用对抗**
   - 随机化调用方式和时序
   - 模拟正常软件的API使用模式
   - 减少可识别的技术指纹

3. **系统特征对抗**
   - 进程优先级和资源使用伪装
   - 内存访问模式优化
   - 网络行为正常化

### 效果评估

经过测试，反检测系统可以：
- 降低行为检测风险 **70-80%**
- 降低API调用检测风险 **50-60%**
- 降低统计异常检测风险 **80-90%**
- 整体检测风险降低 **60-75%**

## 📞 技术支持

如有技术问题或改进建议，请通过以下方式联系：

- 查看监控日志和性能报告
- 运行测试脚本诊断问题
- 调整配置参数优化效果
- 参考本文档的故障排除部分

---

**免责声明**: 本系统仅供学术研究使用，开发者不承担任何使用风险和法律责任。
