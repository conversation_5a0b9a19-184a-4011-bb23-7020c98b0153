{"fov_size": 247, "pid_kp_x": 0.26, "pid_ki_x": 0.0, "pid_kd_x": 0.0, "pid_kp_y": 0.26, "pid_ki_y": 0.0, "pid_kd_y": 0.0, "aim_part": "head", "AimKeys": [1, 6, 2], "auto_fire_key": 6, "auto_fire_delay": 0.0, "auto_fire_interval": 0.18, "auto_fire_target_part": "both", "min_confidence": 0.57, "show_confidence": true, "detect_interval": 0.01, "keep_detecting": true, "fov_follow_mouse": false, "aim_toggle_key": 45, "model_path": "模型\\Roblox.onnx", "auto_fire_key2": 4, "AimToggle": true, "show_fov": true, "show_boxes": true, "show_status_panel": true, "single_target_mode": true, "head_width_ratio": 0.38, "head_height_ratio": 0.26, "body_width_ratio": 0.87, "performance_mode": true, "max_queue_size": 1, "cpu_optimization": true, "thread_priority": "high", "process_priority": "high", "cpu_affinity": null, "enable_sound_alert": true, "sound_frequency": 1000, "sound_duration": 100, "sound_interval": 200, "advanced_mode": true, "mouse_move_method": "SetCursorPos", "anti_detection_enabled": true, "human_reaction_time_base": 0.18, "human_skill_level": 0.75, "human_consistency": 0.8, "human_fatigue_rate": 0.001, "target_accuracy": 0.25, "target_headshot_rate": 0.15, "target_kd_ratio": 1.2, "adaptive_capture_enabled": true, "capture_roi_enabled": true, "api_randomization_enabled": true, "mouse_method_randomization": true}