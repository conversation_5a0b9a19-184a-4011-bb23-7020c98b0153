# behavior_simulator.py - 人类行为模拟核心模块
# 实现人性化行为模拟、API混淆、进程伪装等系统增强功能

import random
import time
import math
import numpy as np
import threading
import psutil
import ctypes
from typing import Tuple, List, Optional
from dataclasses import dataclass

@dataclass
class HumanProfile:
    """人类玩家特征配置文件"""
    reaction_time_base: float = 0.18  # 基础反应时间(秒)
    reaction_time_variance: float = 0.05  # 反应时间方差
    skill_level: float = 0.75  # 技能水平 (0-1)
    fatigue_rate: float = 0.001  # 疲劳累积速率
    stress_sensitivity: float = 0.3  # 压力敏感度
    consistency: float = 0.8  # 操作一致性
    mouse_sensitivity: float = 1.0  # 鼠标灵敏度
    
class HumanBehaviorSimulator:
    """
    人类行为模拟器 - P0优先级
    模拟真实玩家的反应时间、操作习惯、疲劳状态等
    """
    
    def __init__(self, profile: Optional[HumanProfile] = None):
        self.profile = profile or HumanProfile()
        self.session_start_time = time.time()
        self.total_actions = 0
        self.recent_performance = []
        self.current_stress = 0.0
        self.fatigue_level = 0.0
        
        # 个人化参数 - 每次启动随机生成
        self._randomize_personal_traits()
    
    def _randomize_personal_traits(self):
        """随机化个人特征，模拟不同玩家"""
        # 在基础值附近随机化
        variance = 0.2
        self.profile.reaction_time_base *= random.uniform(1-variance, 1+variance)
        self.profile.skill_level *= random.uniform(1-variance, 1+variance)
        self.profile.consistency *= random.uniform(1-variance, 1+variance)
        
        # 确保值在合理范围内
        self.profile.reaction_time_base = max(0.08, min(0.35, self.profile.reaction_time_base))
        self.profile.skill_level = max(0.3, min(1.0, self.profile.skill_level))
        self.profile.consistency = max(0.5, min(0.95, self.profile.consistency))
    
    def get_human_reaction_delay(self) -> float:
        """
        获取人性化的反应延迟
        考虑疲劳、压力、个人差异等因素
        """
        # 基础反应时间 + 随机变化
        base_delay = random.normalvariate(
            self.profile.reaction_time_base, 
            self.profile.reaction_time_variance
        )
        
        # 疲劳影响 - 游戏时间越长反应越慢
        fatigue_penalty = self.fatigue_level * 0.1
        
        # 压力影响 - 高压情况下反应时间增加
        stress_penalty = self.current_stress * self.profile.stress_sensitivity * 0.05
        
        # 技能水平影响
        skill_bonus = (1.0 - self.profile.skill_level) * 0.08
        
        total_delay = base_delay + fatigue_penalty + stress_penalty + skill_bonus
        
        # 确保延迟在合理范围内
        return max(0.05, min(0.5, total_delay))
    
    def should_make_mistake(self) -> bool:
        """
        判断是否应该故意犯错
        基于技能水平、疲劳、压力等因素
        """
        # 基础失误率
        base_error_rate = 1.0 - self.profile.skill_level
        
        # 疲劳增加失误率
        fatigue_error = self.fatigue_level * 0.3
        
        # 压力增加失误率
        stress_error = self.current_stress * 0.2
        
        total_error_rate = base_error_rate + fatigue_error + stress_error
        total_error_rate = min(0.4, total_error_rate)  # 最大40%失误率
        
        return random.random() < total_error_rate
    
    def get_aim_offset(self, distance_to_target: float) -> Tuple[float, float]:
        """
        获取瞄准偏移量，模拟人类不完美的瞄准
        """
        if self.should_make_mistake():
            # 故意失误 - 较大偏移
            max_offset = min(50, distance_to_target * 0.3)
            offset_x = random.uniform(-max_offset, max_offset)
            offset_y = random.uniform(-max_offset, max_offset)
        else:
            # 正常偏移 - 基于技能水平
            skill_factor = 1.0 - self.profile.skill_level
            base_offset = skill_factor * 10
            
            # 疲劳和压力增加偏移
            fatigue_offset = self.fatigue_level * 8
            stress_offset = self.current_stress * 6
            
            total_offset = base_offset + fatigue_offset + stress_offset
            offset_x = random.normalvariate(0, total_offset)
            offset_y = random.normalvariate(0, total_offset)
        
        return offset_x, offset_y
    
    def update_session_state(self, action_success: bool = True):
        """
        更新会话状态 - 疲劳、压力等
        """
        self.total_actions += 1
        
        # 更新疲劳 - 随时间累积
        session_duration = time.time() - self.session_start_time
        self.fatigue_level = min(1.0, session_duration * self.profile.fatigue_rate)
        
        # 更新压力 - 基于最近表现
        self.recent_performance.append(action_success)
        if len(self.recent_performance) > 10:
            self.recent_performance.pop(0)
        
        # 连续失败增加压力
        recent_failures = sum(1 for x in self.recent_performance if not x)
        self.current_stress = min(1.0, recent_failures / 10.0)
    
    def get_mouse_movement_style(self) -> str:
        """
        获取当前应使用的鼠标移动风格
        随机化以避免检测
        """
        styles = ['smooth', 'jerky', 'overshoot', 'undershoot']
        weights = [0.4, 0.2, 0.2, 0.2]  # smooth最常见
        
        # 疲劳时更容易出现不稳定移动
        if self.fatigue_level > 0.5:
            weights = [0.2, 0.4, 0.3, 0.1]
        
        return random.choices(styles, weights=weights)[0]

class AdaptiveScreenCapture:
    """
    自适应屏幕截图 - P1优先级
    智能调整截图频率和区域，减少API调用特征
    """
    
    def __init__(self):
        self.last_capture_time = 0
        self.base_interval = 1/60  # 基础60fps
        self.current_interval = self.base_interval
        self.capture_count = 0
        self.performance_mode = False
        
        # 动态区域跟踪
        self.roi_history = []
        self.adaptive_roi = True
        
    def should_capture_now(self, game_state: str = "normal") -> bool:
        """
        判断是否应该现在截图
        根据游戏状态动态调整频率
        """
        current_time = time.time()
        
        # 根据游戏状态调整间隔
        if game_state == "combat":
            target_interval = self.base_interval  # 战斗中保持高频
        elif game_state == "menu":
            target_interval = 0.2  # 菜单中降低到5fps
        else:
            target_interval = self.base_interval * 1.5  # 正常情况略微降低
        
        # 添加随机抖动避免规律性
        jitter = random.uniform(0.8, 1.2)
        actual_interval = target_interval * jitter
        
        if current_time - self.last_capture_time >= actual_interval:
            self.last_capture_time = current_time
            self.capture_count += 1
            return True
        
        return False
    
    def get_capture_region(self, full_screen_size: Tuple[int, int]) -> dict:
        """
        获取优化的截图区域
        动态调整以减少不必要的截图面积
        """
        width, height = full_screen_size
        
        if not self.adaptive_roi:
            # 全屏截图
            return {
                "left": 0,
                "top": 0, 
                "width": width,
                "height": height
            }
        
        # 智能ROI - 只截图中心区域
        roi_width = min(width, 1280)  # 最大1280宽度
        roi_height = min(height, 720)  # 最大720高度
        
        left = (width - roi_width) // 2
        top = (height - roi_height) // 2
        
        return {
            "left": left,
            "top": top,
            "width": roi_width, 
            "height": roi_height
        }

class NaturalMouseMovement:
    """
    自然鼠标移动 - P0优先级
    生成人性化的鼠标移动轨迹
    """
    
    def __init__(self):
        self.last_move_time = 0
        self.movement_history = []
        
    def generate_human_trajectory(self, start_pos: Tuple[float, float], 
                                 end_pos: Tuple[float, float],
                                 movement_style: str = "smooth") -> List[Tuple[float, float]]:
        """
        生成人性化的鼠标移动轨迹
        """
        start_x, start_y = start_pos
        end_x, end_y = end_pos
        
        # 性能优化：使用平方距离进行初步判断，避免开方运算
        dx = end_x - start_x
        dy = end_y - start_y
        distance_squared = dx*dx + dy*dy

        if distance_squared < 25:  # 5*5 = 25
            return [end_pos]

        distance = math.sqrt(distance_squared)

        # 距离检查已在上面完成，这里不需要重复检查
        
        # 根据距离决定移动点数
        num_points = max(3, min(15, int(distance / 20)))
        
        trajectory = []
        
        if movement_style == "smooth":
            trajectory = self._generate_smooth_curve(start_pos, end_pos, num_points)
        elif movement_style == "jerky":
            trajectory = self._generate_jerky_movement(start_pos, end_pos, num_points)
        elif movement_style == "overshoot":
            trajectory = self._generate_overshoot_movement(start_pos, end_pos, num_points)
        else:  # undershoot
            trajectory = self._generate_undershoot_movement(start_pos, end_pos, num_points)
        
        return trajectory
    
    def _generate_smooth_curve(self, start: Tuple[float, float], 
                              end: Tuple[float, float], 
                              num_points: int) -> List[Tuple[float, float]]:
        """生成平滑的贝塞尔曲线"""
        # 简化的贝塞尔曲线实现
        points = []
        for i in range(num_points + 1):
            t = i / num_points
            # 二次贝塞尔曲线
            x = start[0] * (1-t)**2 + 2 * (start[0] + end[0])/2 * (1-t) * t + end[0] * t**2
            y = start[1] * (1-t)**2 + 2 * (start[1] + end[1])/2 * (1-t) * t + end[1] * t**2
            
            # 添加微小的随机抖动
            noise_x = random.uniform(-1, 1)
            noise_y = random.uniform(-1, 1)
            
            points.append((x + noise_x, y + noise_y))
        
        return points
    
    def _generate_jerky_movement(self, start: Tuple[float, float], 
                                end: Tuple[float, float], 
                                num_points: int) -> List[Tuple[float, float]]:
        """生成不稳定的移动轨迹"""
        points = []
        for i in range(num_points + 1):
            t = i / num_points
            
            # 线性插值 + 较大随机偏移
            x = start[0] + (end[0] - start[0]) * t
            y = start[1] + (end[1] - start[1]) * t
            
            # 添加较大的随机偏移
            if i > 0 and i < num_points:  # 不偏移起点和终点
                noise_x = random.uniform(-5, 5)
                noise_y = random.uniform(-5, 5)
                x += noise_x
                y += noise_y
            
            points.append((x, y))
        
        return points
    
    def _generate_overshoot_movement(self, start: Tuple[float, float], 
                                    end: Tuple[float, float], 
                                    num_points: int) -> List[Tuple[float, float]]:
        """生成过冲修正的移动轨迹"""
        # 先过冲，再修正
        overshoot_factor = random.uniform(1.1, 1.3)
        
        # 计算过冲点
        direction_x = end[0] - start[0]
        direction_y = end[1] - start[1]
        overshoot_x = end[0] + direction_x * (overshoot_factor - 1)
        overshoot_y = end[1] + direction_y * (overshoot_factor - 1)
        
        # 生成到过冲点的轨迹
        mid_points = num_points // 2
        trajectory1 = self._generate_smooth_curve(start, (overshoot_x, overshoot_y), mid_points)
        
        # 生成从过冲点到目标的修正轨迹
        trajectory2 = self._generate_smooth_curve((overshoot_x, overshoot_y), end, num_points - mid_points)
        
        return trajectory1 + trajectory2[1:]  # 避免重复过冲点
    
    def _generate_undershoot_movement(self, start: Tuple[float, float], 
                                     end: Tuple[float, float], 
                                     num_points: int) -> List[Tuple[float, float]]:
        """生成欠冲补偿的移动轨迹"""
        # 先到达目标附近，再微调
        undershoot_factor = random.uniform(0.8, 0.95)
        
        # 计算欠冲点
        direction_x = end[0] - start[0]
        direction_y = end[1] - start[1]
        undershoot_x = start[0] + direction_x * undershoot_factor
        undershoot_y = start[1] + direction_y * undershoot_factor
        
        # 生成到欠冲点的轨迹
        mid_points = num_points // 2
        trajectory1 = self._generate_smooth_curve(start, (undershoot_x, undershoot_y), mid_points)
        
        # 生成从欠冲点到目标的补偿轨迹
        trajectory2 = self._generate_smooth_curve((undershoot_x, undershoot_y), end, num_points - mid_points)
        
        return trajectory1 + trajectory2[1:]  # 避免重复欠冲点

class PerformanceController:
    """
    性能控制器 - P0优先级
    智能控制命中率、爆头率等关键指标
    """

    def __init__(self):
        self.target_accuracy = 0.25  # 目标命中率25%
        self.target_headshot_rate = 0.15  # 目标爆头率15%
        self.max_accuracy = 0.35  # 最大命中率限制

        # 性能窗口 - 只保留最近的数据
        self.performance_window = []
        self.window_size = 50  # 最近50发

        # 会话统计
        self.session_stats = {
            'shots_fired': 0,
            'shots_hit': 0,
            'headshots': 0,
            'session_start': time.time()
        }

    def should_assist_aim(self) -> bool:
        """
        判断是否应该提供瞄准辅助
        基于当前命中率动态调整
        """
        if len(self.performance_window) < 5:
            return True  # 前几发正常辅助

        current_accuracy = self._get_current_accuracy()

        # 如果命中率过高，降低辅助概率
        if current_accuracy > self.max_accuracy:
            return False
        elif current_accuracy > self.target_accuracy:
            # 在目标命中率附近时，随机决定是否辅助
            excess_ratio = (current_accuracy - self.target_accuracy) / (self.max_accuracy - self.target_accuracy)
            assist_probability = 1.0 - excess_ratio * 0.7  # 最多降低70%概率
            return random.random() < assist_probability
        else:
            return True  # 低于目标时正常辅助

    def should_aim_for_head(self) -> bool:
        """
        判断是否应该瞄准头部
        控制爆头率在合理范围内
        """
        if self.session_stats['shots_hit'] == 0:
            return random.random() < 0.3  # 初始30%概率

        current_headshot_rate = self.session_stats['headshots'] / self.session_stats['shots_hit']

        if current_headshot_rate > self.target_headshot_rate * 1.5:
            return False  # 爆头率过高，强制瞄准身体
        elif current_headshot_rate > self.target_headshot_rate:
            # 在目标附近时随机决定
            return random.random() < 0.3
        else:
            return random.random() < 0.6  # 低于目标时增加爆头概率

    def record_shot(self, hit: bool, headshot: bool = False):
        """记录射击结果"""
        self.session_stats['shots_fired'] += 1

        if hit:
            self.session_stats['shots_hit'] += 1
            if headshot:
                self.session_stats['headshots'] += 1

        # 更新性能窗口
        self.performance_window.append({
            'hit': hit,
            'headshot': headshot,
            'timestamp': time.time()
        })

        # 保持窗口大小
        if len(self.performance_window) > self.window_size:
            self.performance_window.pop(0)

    def _get_current_accuracy(self) -> float:
        """获取当前命中率"""
        if self.session_stats['shots_fired'] == 0:
            return 0.0
        return self.session_stats['shots_hit'] / self.session_stats['shots_fired']

    def get_performance_stats(self) -> dict:
        """获取性能统计"""
        current_accuracy = self._get_current_accuracy()
        current_headshot_rate = 0.0

        if self.session_stats['shots_hit'] > 0:
            current_headshot_rate = self.session_stats['headshots'] / self.session_stats['shots_hit']

        return {
            'accuracy': current_accuracy,
            'headshot_rate': current_headshot_rate,
            'total_shots': self.session_stats['shots_fired'],
            'total_hits': self.session_stats['shots_hit'],
            'total_headshots': self.session_stats['headshots'],
            'session_duration': time.time() - self.session_stats['session_start']
        }

class ProcessCamouflage:
    """
    进程伪装 - P2优先级
    修改进程特征，降低被检测的风险
    """

    def __init__(self):
        self.original_name = None
        self.disguised = False

    def apply_disguise(self) -> bool:
        """
        应用进程伪装
        修改进程名称和其他特征
        """
        try:
            # 获取当前进程
            current_process = psutil.Process()
            self.original_name = current_process.name()

            # 这里可以添加更多伪装逻辑
            # 注意：某些操作可能需要管理员权限

            self.disguised = True
            return True

        except Exception as e:
            print(f"[进程伪装] 应用失败: {e}")
            return False

    def remove_disguise(self) -> bool:
        """移除进程伪装，恢复原始状态"""
        try:
            if self.disguised and self.original_name:
                # 恢复原始状态的逻辑
                self.disguised = False
                return True
            return True

        except Exception as e:
            print(f"[进程伪装] 移除失败: {e}")
            return False

class BehaviorManager:
    """
    行为管理器 - 统一管理所有行为模拟组件
    """

    def __init__(self):
        self.enabled = True
        self.human_behavior = HumanBehaviorSimulator()
        self.screen_capture = AdaptiveScreenCapture()
        self.mouse_movement = NaturalMouseMovement()
        self.performance_controller = PerformanceController()
        self.process_camouflage = ProcessCamouflage()

        # 初始化状态
        self.initialized = False

    def initialize(self) -> bool:
        """初始化行为管理器"""
        try:
            print("[行为模拟] 正在初始化...")

            # 应用进程伪装
            if self.process_camouflage.apply_disguise():
                print("[行为模拟] ✅ 进程伪装已应用")
            else:
                print("[行为模拟] ⚠️ 进程伪装应用失败")

            self.initialized = True
            print("[行为模拟] ✅ 初始化完成")
            return True

        except Exception as e:
            print(f"[行为模拟] ❌ 初始化失败: {e}")
            return False

    def cleanup(self):
        """清理资源"""
        try:
            if self.process_camouflage.disguised:
                self.process_camouflage.remove_disguise()
            print("[行为模拟] 清理完成")
        except Exception as e:
            print(f"[行为模拟] 清理失败: {e}")

    def get_status_report(self) -> dict:
        """获取状态报告"""
        return {
            'enabled': self.enabled,
            'initialized': self.initialized,
            'process_disguised': self.process_camouflage.disguised,
            'human_profile': {
                'reaction_time': self.human_behavior.profile.reaction_time_base,
                'skill_level': self.human_behavior.profile.skill_level,
                'fatigue_level': self.human_behavior.fatigue_level,
                'stress_level': self.human_behavior.current_stress
            },
            'performance': self.performance_controller.get_performance_stats()
        }

# 创建全局实例
behavior_manager = BehaviorManager()
