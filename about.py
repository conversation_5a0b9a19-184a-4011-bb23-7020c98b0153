# about.py - 系统信息显示模块
# 系统增强改进：伪装为系统工具的关于窗口
import tkinter as tk
from tkinter import ttk
import webbrowser
import os
import random
import time
from PIL import Image, ImageTk
from language_manager import get_text

# PIL 兼容性处理
try:
    LANCZOS_FILTER = Image.Resampling.LANCZOS
except AttributeError:
    LANCZOS_FILTER = Image.LANCZOS

class AboutWindow:
    """
    关于窗口类 - 伪装为系统优化工具
    系统增强特性：
    1. 窗口标题伪装
    2. 延迟显示模拟正常软件加载
    3. 随机化窗口行为
    """
    def __init__(self, parent=None):
        self.parent = parent
        self.window = tk.Toplevel(parent) if parent else tk.Tk()

        # 系统增强：伪装窗口标题
        disguised_title = self._get_disguised_title()
        self.window.title(disguised_title)
        self.window.geometry('500x400')
        self.window.resizable(False, False)

        # 颜色设置 (与主程序保持一致)
        self.bg_main = "#250526"
        self.bg_frame = '#120606'
        self.fg_text = '#e0e6f0'
        self.accent = '#230622'
        self.btn_bg = '#230622'
        self.btn_active = '#FF0000'

        self.window.configure(bg=self.bg_main)

        # 系统增强：模拟正常软件的加载延迟
        self._simulate_loading_delay()

        # 设置窗口icon
        try:
            icon_path = os.path.join(os.path.dirname(__file__), 'logo.ico')
            if os.path.exists(icon_path):
                self.window.iconbitmap(icon_path)
        except:
            pass

        # 使窗口居中
        self.center_window()

        # 创建内容
        self.create_widgets()

        # 设置为模态窗口
        if parent:
            self.window.transient(parent)
            self.window.grab_set()

        # 绑定关闭事件
        self.window.protocol("WM_DELETE_WINDOW", self.on_close)

    def _get_disguised_title(self):
        """获取伪装的窗口标题"""
        disguised_titles = [
            "系统优化工具 - 关于",
            "性能监控器 - 信息",
            "硬件检测工具 - 关于",
            "系统诊断程序 - 版本信息"
        ]
        return random.choice(disguised_titles)

    def _simulate_loading_delay(self):
        """模拟正常软件的加载延迟"""
        # 随机延迟50-150ms，模拟正常软件加载
        delay = random.uniform(0.05, 0.15)
        time.sleep(delay)

    def center_window(self):
        """将窗口居中显示"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')

    def create_widgets(self):
        """创建窗口内容"""
        main_frame = tk.Frame(self.window, bg=self.bg_main, padx=30, pady=30)
        main_frame.pack(fill="both", expand=True)

        # Logo区域
        logo_frame = tk.Frame(main_frame, bg=self.bg_main)
        logo_frame.pack(pady=(0, 20))

        try:
            logo_path = os.path.join(os.path.dirname(__file__), 'logo.png')
            if os.path.exists(logo_path):
                logo_img = Image.open(logo_path)
                logo_img = logo_img.resize((80, 80), LANCZOS_FILTER)
                self.logo_photo = ImageTk.PhotoImage(logo_img)
                tk.Label(logo_frame, image=self.logo_photo, bg=self.bg_main).pack()
        except:
            # 如果载入logo失败，显示文字替代
            tk.Label(logo_frame, text="Axiom V4", font=("Arial", 20, "bold"),
                    bg=self.bg_main, fg=self.fg_text).pack()

        # 项目标题
        title_label = tk.Label(main_frame, text="Axiom V4",
                              font=("Arial", 24, "bold"),
                              bg=self.bg_main, fg=self.fg_text)
        title_label.pack(pady=(0, 10))

        # 副标题
        subtitle_label = tk.Label(main_frame, text=get_text("about_subtitle"),
                                 font=("Arial", 12),
                                 bg=self.bg_main, fg="#CCCCCC")
        subtitle_label.pack(pady=(0, 20))

        # 分隔线
        separator = tk.Frame(main_frame, height=2, bg=self.accent)
        separator.pack(fill="x", pady=15)

        # "我是人类"文字
        human_label = tk.Label(main_frame, text=get_text("i_am_human"),
                              font=("Arial", 16, "bold"),
                              bg=self.bg_main, fg=self.fg_text)
        human_label.pack(pady=(10, 20))

        # 按钮区域
        button_frame = tk.Frame(main_frame, bg=self.bg_main)
        button_frame.pack(pady=15)

        # Discord 按钮
        discord_btn = tk.Button(button_frame,
                               text=f"💬 {get_text('join_discord')}",
                               command=self.open_discord,
                               bg="#5865F2",
                               fg="white",
                               font=("Arial", 11, "bold"),
                               relief="flat",
                               padx=20,
                               pady=10,
                               cursor="hand2")
        discord_btn.pack(side="left", padx=(0, 15))

        # GitHub 按钮
        github_btn = tk.Button(button_frame,
                              text=f"⭐ {get_text('view_github')}",
                              command=self.open_github,
                              bg="#24292e",
                              fg="white",
                              font=("Arial", 11, "bold"),
                              relief="flat",
                              padx=20,
                              pady=10,
                              cursor="hand2")
        github_btn.pack(side="left")

        # 关闭按钮
        close_btn = tk.Button(main_frame,
                             text=get_text("close"),
                             command=self.on_close,
                             bg=self.btn_bg,
                             fg=self.fg_text,
                             font=("Arial", 10),
                             relief="flat",
                             padx=30,
                             pady=8,
                             cursor="hand2")
        close_btn.pack(pady=(20, 0))

        # 版本信息
        version_label = tk.Label(main_frame, text=get_text("version_info"),
                                font=("Arial", 9),
                                bg=self.bg_main, fg="#888888")
        version_label.pack(pady=(15, 0))

        # 鼠标悬停效果
        self.add_hover_effects(discord_btn, "#4752C4", "#5865F2")
        self.add_hover_effects(github_btn, "#1a1f23", "#24292e")
        self.add_hover_effects(close_btn, self.btn_active, self.btn_bg)

    def add_hover_effects(self, button, hover_color, normal_color):
        """为按钮添加鼠标悬停效果"""
        def on_enter(event):
            button.configure(bg=hover_color)

        def on_leave(event):
            button.configure(bg=normal_color)

        button.bind("<Enter>", on_enter)
        button.bind("<Leave>", on_leave)

    def open_discord(self):
        """打开Discord链接"""
        webbrowser.open("https://discord.gg/h4dEh3b8Bt")

    def open_github(self):
        """打开GitHub链接"""
        webbrowser.open("https://github.com/iisHong0w0/Axiom-AI_Aimbot")

    def on_close(self):
        """关闭窗口"""
        if self.parent:
            self.window.grab_release()
        self.window.destroy()

    def show(self):
        """显示窗口"""
        self.window.mainloop()

def show_about_window(parent=None):
    """显示关于窗口的便捷函数"""
    about = AboutWindow(parent)
    return about

# 如果直接执行此文件，显示关于窗口
if __name__ == "__main__":
    about = AboutWindow()
    about.show()