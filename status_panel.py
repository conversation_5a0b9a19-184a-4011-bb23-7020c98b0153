# status_panel.py
import os
import sys
from PyQt6.QtWidgets import QWidget, QApplication
from PyQt6.QtGui import QPainter, QColor, QFont, QPixmap
from PyQt6.QtCore import Qt, QTimer
import ctypes
from ctypes import wintypes
from language_manager import get_text, language_manager  # <-- 修改: 导入 get_text 和 language_manager

# --- 颜色设置 ---
TEXT_COLOR = QColor("#FFFFFF")
AIM_ON_COLOR = QColor("#00AC09")
AIM_OFF_COLOR = QColor("#AF0000")

def draw_text_with_outline(painter, x, y, text, text_color, outline_color=QColor(0,0,0), outline_width=1):
    """在指定位置绘制带有边框的文字"""
    for dx in range(-outline_width, outline_width+1):
        for dy in range(-outline_width, outline_width+1):
            if dx != 0 or dy != 0:
                painter.setPen(outline_color)
                painter.drawText(x+dx, y+dy, text)
    painter.setPen(text_color)
    painter.drawText(x, y, text)

def get_compute_mode_text(config):
    """获取运算模式文字，支持 ONNX 和 PyTorch 模型"""
    model_path = getattr(config, 'model_path', '')

    # 检查是否为 PyTorch 模型
    if model_path.endswith('.pt'):
        # 移除 CUDA 支持，PyTorch 模型只使用 CPU
        return get_text("cpu")

    # ONNX 模型逻辑
    current_provider = getattr(config, 'current_provider', 'CPUExecutionProvider')
    if "Dml" in current_provider:
        return get_text("gpu_directml")
    else:
        return get_text("cpu")

class StatusPanel(QWidget):
    """
    一个独立的 PyQt6 窗口，用于在屏幕左上角显示 Axiom 的 Logo 和运行状态。
    这个窗口会保持在最上层，且鼠标事件会穿透它，不会影响游戏操作。
    """
    def __init__(self, config):
        super().__init__()
        self.config = config

        # --- 窗口基本设置 ---
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint |    # 无边框
            Qt.WindowType.WindowStaysOnTopHint |   # 总在最前
            Qt.WindowType.Tool |                   # 不在任务栏显示
            Qt.WindowType.WindowTransparentForInput  # 关键：透明输入
        )
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        self.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents, True)

        # --- 设置窗口位置与大小 (可根据需求调整) ---
        self.setGeometry(10, 10, 350, 150)

        # --- 载入 Logo ---
        logo_path = 'logo.png'
        if os.path.exists(logo_path):
            self.logo = QPixmap(logo_path).scaled(48, 48, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
        else:
            self.logo = None
            print("状态面板警告: 在目录中找不到 logo.png")

        # --- 定时更新 ---
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_display)
        # 优化：状态面板更新频率较低即可，因为状态变化不频繁
        self.timer.start(500)  # 降低到500ms，减少不必要的重绘

        # 优化：缓存上一次的状态，避免不必要的重绘
        self.last_aim_state = None
        self.last_provider = None
        self.last_model_path = None
        self.last_language = language_manager.get_current_language()  # 追踪语言变化

    def showEvent(self, event):
        """窗口显示时设置鼠标穿透"""
        super().showEvent(event)
        if sys.platform == "win32":
            # 延迟一下确保窗口完全初始化
            QTimer.singleShot(100, self.set_click_through)

    def set_click_through(self):
        """使用 Win32 API 设置窗口的鼠标穿透属性"""
        if sys.platform != "win32":
            return

        try:
            # 获取窗口句柄 (HWND)
            hwnd = int(self.winId())

            # 定义 Win32 API 常数
            GWL_EXSTYLE = -20
            WS_EX_LAYERED = 0x00080000
            WS_EX_TRANSPARENT = 0x00000020

            # 获取 user32.dll
            user32 = ctypes.windll.user32

            # 获取当前扩展样式
            ex_style = user32.GetWindowLongW(hwnd, GWL_EXSTYLE)

            # 设置新的扩展样式
            new_ex_style = ex_style | WS_EX_LAYERED | WS_EX_TRANSPARENT
            user32.SetWindowLongW(hwnd, GWL_EXSTYLE, new_ex_style)

            print("状态面板: 已成功设置鼠标穿透。")
            return True

        except Exception as e:
            print(f"状态面板错误: 设置鼠标穿透失败 - {e}")
            return False

    def update_display(self):
        """触发重绘事件 - 优化版本，只在状态改变时重绘"""
        # 检查状态是否有变化
        current_aim_state = self.config.AimToggle
        current_provider = getattr(self.config, 'current_provider', 'CPUExecutionProvider')
        current_model_path = getattr(self.config, 'model_path', '')
        current_language = language_manager.get_current_language()  # 检查语言变化

        # 只有状态发生变化时才触发重绘
        if (current_aim_state != self.last_aim_state or
            current_provider != self.last_provider or
            current_model_path != self.last_model_path or
            current_language != self.last_language):  # 新增语言变化检查

            self.last_aim_state = current_aim_state
            self.last_provider = current_provider
            self.last_model_path = current_model_path
            self.last_language = current_language  # 更新语言状态
            self.update()  # 触发重绘

    def paintEvent(self, event):
        """
        绘制事件，所有绘图逻辑都在此处。
        """
        if not getattr(self.config, 'show_status_panel', False):
            return

        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        axiom_font = QFont('Arial', 24, QFont.Weight.Bold)
        status_font = QFont('Arial', 10, QFont.Weight.Bold)
        text_color = TEXT_COLOR

        x_offset, y_offset = 10, 10

        if self.logo:
            painter.drawPixmap(x_offset, y_offset, self.logo)
            x_offset += self.logo.width() + 10

        painter.setFont(axiom_font)
        draw_text_with_outline(painter, x_offset, y_offset + 35, "Axiom", text_color)

        y_offset += 60
        x_offset = 10
        painter.setFont(status_font)

        # --- 修改开始: 使用 get_text 实现国际化 ---
        is_aim_on = self.config.AimToggle
        aim_status_text = get_text("status_panel_on") if is_aim_on else get_text("status_panel_off")
        aim_color = AIM_ON_COLOR if is_aim_on else AIM_OFF_COLOR
        draw_text_with_outline(painter, x_offset, y_offset, f"{get_text('auto_aim')}: {aim_status_text}", aim_color)
        y_offset += 20

        # ***** 系统增强状态显示 *****
        try:
            from behavior_simulator import behavior_manager
            status = behavior_manager.get_status_report()

            # 系统增强状态
            enhancement_enabled = status.get('enabled', False)
            enhancement_text = "🛡️ 启用" if enhancement_enabled else "❌ 禁用"
            enhancement_color = AIM_ON_COLOR if enhancement_enabled else AIM_OFF_COLOR
            draw_text_with_outline(painter, x_offset, y_offset, f"系统增强: {enhancement_text}", enhancement_color)
            y_offset += 20

            # 人类行为状态
            if enhancement_enabled:
                human_profile = status.get('human_profile', {})
                reaction_time = human_profile.get('reaction_time', 0)
                skill_level = human_profile.get('skill_level', 0)
                draw_text_with_outline(painter, x_offset, y_offset, f"反应时间: {reaction_time:.3f}s", text_color)
                y_offset += 20
                draw_text_with_outline(painter, x_offset, y_offset, f"技能等级: {skill_level:.2f}", text_color)
                y_offset += 20
        except ImportError:
            # 系统增强模块未安装
            draw_text_with_outline(painter, x_offset, y_offset, "系统增强: 未安装", AIM_OFF_COLOR)
            y_offset += 20
        except Exception as e:
            # 其他错误
            draw_text_with_outline(painter, x_offset, y_offset, "系统增强: 错误", AIM_OFF_COLOR)
            y_offset += 20

        # 使用新的函数来获取运算模式文字
        provider_text = get_compute_mode_text(self.config)
        draw_text_with_outline(painter, x_offset, y_offset, f"{get_text('status_panel_compute_mode')}: {provider_text}", text_color)
        y_offset += 20

        model_name = os.path.basename(self.config.model_path)
        draw_text_with_outline(painter, x_offset, y_offset, f"{get_text('status_panel_current_model')}: {model_name}", text_color)