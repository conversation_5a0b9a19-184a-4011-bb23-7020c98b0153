# win_utils.py
import ctypes
import win32api
import win32con
import time
import random
import threading
from language_manager import get_text

# Windows 虚拟按键码对应英文名称
VK_CODE_MAP = {
    0x01: "Mouse Left", 0x02: "Mouse Right", 0x04: "Mouse Middle", 0x05: "Mouse X1",
    0x06: "Mouse X2", 0x08: "Backspace", 0x09: "Tab", 0x0D: "Enter",
    0x10: "Shift", 0x11: "Ctrl", 0x12: "Alt", 0x14: "CapsLock",
    0x1B: "Esc", 0x20: "Space", 0x25: "Left", 0x26: "Up", 0x27: "Right",
    0x28: "Down", 0x2C: "PrintScreen", 0x2D: "Insert", 0x2E: "Delete",
    0x30: "0", 0x31: "1", 0x32: "2", 0x33: "3", 0x34: "4", 0x35: "5",
    0x36: "6", 0x37: "7", 0x38: "8", 0x39: "9", 0x41: "A", 0x42: "B",
    0x43: "C", 0x44: "D", 0x45: "E", 0x46: "F", 0x47: "G", 0x48: "H",
    0x49: "I", 0x4A: "J", 0x4B: "K", 0x4C: "L", 0x4D: "M", 0x4E: "N",
    0x4F: "O", 0x50: "P", 0x51: "Q", 0x52: "R", 0x53: "S", 0x54: "T",
    0x55: "U", 0x56: "V", 0x57: "W", 0x58: "X", 0x59: "Y", 0x5A: "Z",
    0x5B: "Win", 0x60: "Num0", 0x61: "Num1", 0x62: "Num2", 0x63: "Num3",
    0x64: "Num4", 0x65: "Num5", 0x66: "Num6", 0x67: "Num7", 0x68: "Num8",
    0x69: "Num9", 0x70: "F1", 0x71: "F2", 0x72: "F3", 0x73: "F4",
    0x74: "F5", 0x75: "F6", 0x76: "F7", 0x77: "F8", 0x78: "F9",
    0x79: "F10", 0x7A: "F11", 0x7B: "F12", 0x90: "NumLock", 0x91: "ScrollLock",
    0xA0: "Shift(L)", 0xA1: "Shift(R)", 0xA2: "Ctrl(L)", 0xA3: "Ctrl(R)",
    0xA4: "Alt(L)", 0xA5: "Alt(R)",
}

# 按键名称多语言对应表
VK_TRANSLATIONS = {
    "zh_tw": {
        "Mouse Left": "鼠标左键", "Mouse Right": "鼠标右键", "Mouse Middle": "鼠标中键", "Mouse X1": "鼠标侧键1",
        "Mouse X2": "鼠标侧键2", "Backspace": "Backspace", "Tab": "Tab", "Enter": "Enter",
        "Shift": "Shift", "Ctrl": "Ctrl", "Alt": "Alt", "CapsLock": "CapsLock",
        "Esc": "Esc", "Space": "Space", "Left": "←", "Up": "↑", "Right": "→",
        "Down": "↓", "PrintScreen": "PrintScreen", "Insert": "Insert", "Delete": "Delete",
        "Num0": "数字键0", "Num1": "数字键1", "Num2": "数字键2", "Num3": "数字键3", "Num4": "数字键4",
        "Num5": "数字键5", "Num6": "数字键6", "Num7": "数字键7", "Num8": "数字键8", "Num9": "数字键9",
        "F1": "F1", "F2": "F2", "F3": "F3", "F4": "F4", "F5": "F5", "F6": "F6", "F7": "F7", "F8": "F8", "F9": "F9", "F10": "F10", "F11": "F11", "F12": "F12",
        "Win": "Win", "Shift(L)": "Shift(左)", "Shift(R)": "Shift(右)", "Ctrl(L)": "Ctrl(左)", "Ctrl(R)": "Ctrl(右)", "Alt(L)": "Alt(左)", "Alt(R)": "Alt(右)"
    },
    "en": {}  # 英文直接显示原名
}

def get_vk_name(key_code):
    name = VK_CODE_MAP.get(key_code, f'0x{key_code:02X}')
    lang = None
    try:
        from language_manager import language_manager
        lang = language_manager.get_current_language()
    except Exception:
        lang = "zh_tw"
    if lang != "en":
        return VK_TRANSLATIONS.get(lang, {}).get(name, name)
    return name

# ===== 鼠标移动方式 =====

# 方式1: 原始 SendInput (容易被检测)
class MOUSEINPUT(ctypes.Structure):
    _fields_ = [
        ("dx", ctypes.c_long),
        ("dy", ctypes.c_long),
        ("mouseData", ctypes.c_ulong),
        ("dwFlags", ctypes.c_ulong),
        ("time", ctypes.c_ulong),
        ("dwExtraInfo", ctypes.POINTER(ctypes.c_ulong))
    ]

class INPUT(ctypes.Structure):
    class _INPUT_UNION(ctypes.Union):
        _fields_ = [("mi", MOUSEINPUT)]
    _anonymous_ = ("u",)
    _fields_ = [("type", ctypes.c_ulong), ("u", _INPUT_UNION)]

INPUT_MOUSE = 0
MOUSEEVENTF_MOVE = 0x0001

def send_mouse_move_sendinput(dx, dy):
    """方式1: SendInput API (原始方式，容易被检测)"""
    print(f"[鼠标移动] 使用 SendInput 方式，移动: ({dx}, {dy})")
    extra = ctypes.c_ulong(0)
    ii_ = INPUT._INPUT_UNION()
    ii_.mi = MOUSEINPUT(dx, dy, 0, MOUSEEVENTF_MOVE, 0, ctypes.pointer(extra))
    command = INPUT(INPUT_MOUSE, ii_)
    ctypes.windll.user32.SendInput(1, ctypes.byref(command), ctypes.sizeof(command))

# 方式2: 硬件层级模拟
def send_mouse_move_hardware(dx, dy):
    """方式2: 硬件层级鼠标移动模拟"""
    print(f"[鼠标移动] 使用硬件层级方式，移动: ({dx}, {dy})")
    try:
        win32api.mouse_event(win32con.MOUSEEVENTF_MOVE, dx, dy, 0, 0)
    except Exception as e:
        print(f"硬件层级移动失败: {e}")

# 方式3: mouse_event (异步)
def send_mouse_move_mouse_event(dx, dy):
    """方式3: mouse_event 移动（异步执行）"""
    print(f"[鼠标移动] 使用 mouse_event 方式，移动: ({dx}, {dy})")
    def mouse_event_move():
        try:
            win32api.mouse_event(win32con.MOUSEEVENTF_MOVE, dx, dy, 0, 0)
        except Exception:
            pass

    # 异步执行
    threading.Thread(target=mouse_event_move, daemon=True).start()

# 方式4: SetCursorPos (绝对位置)
def send_mouse_move_setcursorpos(dx, dy):
    """方式4: SetCursorPos 移动（绝对位置）"""
    print(f"[鼠标移动] 使用 SetCursorPos 方式，移动: ({dx}, {dy})")
    try:
        # 获取当前鼠标位置
        current_x, current_y = win32api.GetCursorPos()
        # 计算新位置
        new_x = current_x + dx
        new_y = current_y + dy
        # 设置新位置
        win32api.SetCursorPos((new_x, new_y))
    except Exception as e:
        print(f"SetCursorPos 移动失败: {e}")



# 主要鼠标移动函数 - 反检测增强版本
def send_mouse_move(dx, dy, method="mouse_event"):
    """
    主要鼠标移动函数 - 已集成反检测功能
    method 选项:
    - "SendInput": SendInput (原始方式，容易被检测)
    - "hardware": 硬件层级 (较隐蔽)
    - "mouse_event": mouse_event (很隐蔽)
    - "SetCursorPos": SetCursorPos (绝对位置)
    """
    if abs(dx) < 1 and abs(dy) < 1:
        return  # 移动量太小，跳过

    # ***** 反检测：添加微小的随机延迟 *****
    import random
    import time

    # 0-2ms的随机延迟，模拟人类神经传导延迟
    micro_delay = random.uniform(0.0001, 0.002)
    time.sleep(micro_delay)

    # ***** 反检测：添加微小的移动抖动 *****
    # 模拟人类手部微颤
    jitter_x = random.uniform(-0.5, 0.5)
    jitter_y = random.uniform(-0.5, 0.5)

    adjusted_dx = dx + jitter_x
    adjusted_dy = dy + jitter_y

    if method == "SendInput":
        send_mouse_move_sendinput(int(adjusted_dx), int(adjusted_dy))
    elif method == "hardware":
        send_mouse_move_hardware(int(adjusted_dx), int(adjusted_dy))
    elif method == "mouse_event":
        send_mouse_move_mouse_event(int(adjusted_dx), int(adjusted_dy))
    elif method == "SetCursorPos":
        send_mouse_move_setcursorpos(int(adjusted_dx), int(adjusted_dy))
    else:
        # 默认使用 mouse_event 方式
        send_mouse_move_mouse_event(int(adjusted_dx), int(adjusted_dy))

    # ***** 反检测：记录移动历史，避免规律性 *****
    # 这里可以添加移动模式分析和反规律化逻辑

def is_key_pressed(key_code):
    return win32api.GetAsyncKeyState(key_code) & 0x8000 != 0